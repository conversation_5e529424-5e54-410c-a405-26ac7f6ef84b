"""
Shopify integration routes for exporting products
"""
from flask import Blueprint, request, jsonify
from services.shopify_service import shopify_service
from services.mongodb_service import mongodb_service
import logging
from datetime import datetime, timezone

logger = logging.getLogger(__name__)

shopify_bp = Blueprint('shopify', __name__)

@shopify_bp.route('/test-connection', methods=['GET'])
def test_shopify_connection():
    """Test connection to Shopify API"""
    try:
        result = shopify_service.test_connection()
        return jsonify(result)
    except Exception as e:
        logger.error(f"Error testing Shopify connection: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@shopify_bp.route('/export-product/<product_id>', methods=['POST'])
def export_single_product(product_id):
    """Export a single product to Shopify"""
    try:
        data = request.get_json() or {}
        selected_images = data.get('selected_images', [])  # List of image indices
        main_image_index = data.get('main_image_index', 0)  # Index of main image
        
        # Validate inputs
        if selected_images and not isinstance(selected_images, list):
            return jsonify({
                'success': False,
                'error': 'selected_images must be a list of indices'
            }), 400
        
        if not isinstance(main_image_index, int) or main_image_index < 0:
            return jsonify({
                'success': False,
                'error': 'main_image_index must be a non-negative integer'
            }), 400
        
        # Export product
        result = shopify_service.export_product(
            product_id, 
            selected_images, 
            main_image_index
        )
        
        if result['success']:
            return jsonify(result)
        else:
            return jsonify(result), 400
            
    except Exception as e:
        logger.error(f"Error exporting product {product_id}: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@shopify_bp.route('/bulk-export', methods=['POST'])
def bulk_export_products():
    """Export multiple products to Shopify"""
    try:
        data = request.get_json()
        
        if not data or 'product_ids' not in data:
            return jsonify({
                'success': False,
                'error': 'product_ids is required'
            }), 400
        
        product_ids = data['product_ids']
        
        if not product_ids or not isinstance(product_ids, list):
            return jsonify({
                'success': False,
                'error': 'product_ids must be a non-empty list'
            }), 400
        
        # Export products
        result = shopify_service.bulk_export_products(product_ids)
        
        return jsonify(result)
        
    except Exception as e:
        logger.error(f"Error bulk exporting products: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@shopify_bp.route('/update-images/<shopify_product_id>', methods=['PUT'])
def update_product_images(shopify_product_id):
    """Update images for an existing Shopify product"""
    try:
        data = request.get_json()
        
        if not data or 'product_id' not in data:
            return jsonify({
                'success': False,
                'error': 'product_id is required'
            }), 400
        
        product_id = data['product_id']
        selected_images = data.get('selected_images', [])
        main_image_index = data.get('main_image_index', 0)
        
        # Update images
        result = shopify_service.update_product_images(
            shopify_product_id,
            product_id,
            selected_images,
            main_image_index
        )
        
        if result['success']:
            return jsonify(result)
        else:
            return jsonify(result), 400
            
    except Exception as e:
        logger.error(f"Error updating Shopify product images: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@shopify_bp.route('/exported-products', methods=['GET'])
def get_exported_products():
    """Get list of products that have been exported to Shopify"""
    try:
        # Get query parameters
        page = int(request.args.get('page', 1))
        limit = min(int(request.args.get('limit', 50)), 100)
        skip = (page - 1) * limit
        
        # Find products that have been exported to Shopify
        filter_query = {
            'shopify_product_id': {'$exists': True, '$ne': None},
            'status': 'exported_to_shopify'
        }
        
        # Get products
        cursor = mongodb_service.db.products.find(filter_query).skip(skip).limit(limit)
        products = list(cursor)
        
        # Convert ObjectId to string for JSON serialization
        for product in products:
            product['_id'] = str(product['_id'])
        
        # Get total count
        total_count = mongodb_service.db.products.count_documents(filter_query)
        
        return jsonify({
            'success': True,
            'data': {
                'products': products,
                'pagination': {
                    'page': page,
                    'limit': limit,
                    'total': total_count,
                    'pages': (total_count + limit - 1) // limit
                }
            }
        })
        
    except Exception as e:
        logger.error(f"Error getting exported products: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@shopify_bp.route('/stats', methods=['GET'])
def get_shopify_stats():
    """Get Shopify export statistics"""
    try:
        # Count exported products
        exported_count = mongodb_service.db.products.count_documents({
            'shopify_product_id': {'$exists': True, '$ne': None}
        })
        
        # Count products ready for export (in workspace)
        ready_count = mongodb_service.db.products.count_documents({
            'status': 'in_workspace'
        })
        
        # Count products being processed
        processing_count = mongodb_service.db.products.count_documents({
            'status': 'processing'
        })
        
        return jsonify({
            'success': True,
            'data': {
                'exported_to_shopify': exported_count,
                'ready_for_export': ready_count,
                'processing': processing_count,
                'total_workspace': ready_count + processing_count + exported_count
            }
        })
        
    except Exception as e:
        logger.error(f"Error getting Shopify stats: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@shopify_bp.route('/config', methods=['GET'])
def get_shopify_config():
    """Get Shopify configuration status"""
    try:
        # Check if Shopify is configured
        is_configured = all([
            shopify_service.api_key,
            shopify_service.api_secret,
            shopify_service.store_url
        ])
        
        config_data = {
            'is_configured': is_configured,
            'store_url': shopify_service.store_url if is_configured else None,
            'api_version': '2023-10'
        }
        
        return jsonify({
            'success': True,
            'data': config_data
        })
        
    except Exception as e:
        logger.error(f"Error getting Shopify config: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@shopify_bp.route('/configure', methods=['POST'])
def configure_shopify():
    """Configure Shopify credentials (for development/testing)"""
    try:
        data = request.get_json()
        
        if not data:
            return jsonify({
                'success': False,
                'error': 'Configuration data is required'
            }), 400
        
        # This would typically update environment variables or config
        # For now, we'll just test the provided credentials
        api_key = data.get('api_key')
        api_secret = data.get('api_secret')
        store_url = data.get('store_url')
        
        if not all([api_key, api_secret, store_url]):
            return jsonify({
                'success': False,
                'error': 'api_key, api_secret, and store_url are required'
            }), 400
        
        # Test connection with provided credentials
        temp_service = shopify_service.__class__()
        temp_service.api_key = api_key
        temp_service.api_secret = api_secret
        temp_service.store_url = store_url
        temp_service.base_url = f"https://{store_url}/admin/api/2023-10/products.json"
        temp_service.headers = {
            'Content-Type': 'application/json',
            'X-Shopify-Access-Token': api_secret
        }
        
        test_result = temp_service.test_connection()
        
        if test_result['success']:
            return jsonify({
                'success': True,
                'message': 'Shopify configuration is valid',
                'shop_info': test_result.get('shop_info', {})
            })
        else:
            return jsonify({
                'success': False,
                'error': f"Configuration test failed: {test_result['error']}"
            }), 400
            
    except Exception as e:
        logger.error(f"Error configuring Shopify: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500
