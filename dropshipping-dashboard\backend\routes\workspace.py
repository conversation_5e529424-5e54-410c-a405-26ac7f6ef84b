"""
Workspace management routes for Shopify preparation
"""
from flask import Blueprint, request, jsonify
from services.mongodb_service import mongodb_service
from services.cloudinary_service import cloudinary_service
import logging
from datetime import datetime, timezone

logger = logging.getLogger(__name__)

workspace_bp = Blueprint('workspace', __name__)

@workspace_bp.route('/add', methods=['POST'])
def add_to_workspace():
    """Add products to workspace for Shopify preparation"""
    try:
        data = request.get_json()
        
        if not data or 'product_ids' not in data:
            return jsonify({
                'success': False,
                'error': 'product_ids is required'
            }), 400
        
        product_ids = data['product_ids']
        
        if not product_ids:
            return jsonify({
                'success': False,
                'error': 'At least one product ID is required'
            }), 400
        
        # Update products to mark them as in workspace
        updated_count = 0
        errors = []
        
        for product_id in product_ids:
            try:
                # Update product status to 'in_workspace'
                update_data = {
                    'status': 'in_workspace',
                    'workspace_added_at': datetime.now(timezone.utc),
                    'updated_at': datetime.now(timezone.utc)
                }
                
                success = mongodb_service.update_product(product_id, update_data)
                if success:
                    updated_count += 1
                else:
                    errors.append(f"Failed to update product {product_id}")
            except Exception as e:
                errors.append(f"Error updating product {product_id}: {str(e)}")
        
        return jsonify({
            'success': True,
            'data': {
                'added_count': updated_count,
                'total_requested': len(product_ids),
                'errors': errors
            }
        })
        
    except Exception as e:
        logger.error(f"Error adding products to workspace: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@workspace_bp.route('/products', methods=['GET'])
def get_workspace_products():
    """Get products in workspace"""
    try:
        # Get query parameters
        page = int(request.args.get('page', 1))
        limit = min(int(request.args.get('limit', 50)), 100)
        skip = (page - 1) * limit
        
        # Get products with status 'in_workspace' (explicitly added to workspace)
        filter_query = {
            'status': 'in_workspace',
            'workspace_added_at': {'$exists': True, '$ne': None}
        }
        
        # Get total count
        total_count = mongodb_service.db.products.count_documents(filter_query)
        
        # Get products
        products_cursor = mongodb_service.db.products.find(filter_query).skip(skip).limit(limit).sort('workspace_added_at', -1)
        products = []
        
        for product in products_cursor:
            product['_id'] = str(product['_id'])
            products.append(product)
        
        # Calculate pagination
        total_pages = (total_count + limit - 1) // limit
        
        return jsonify({
            'success': True,
            'data': {
                'products': products,
                'pagination': {
                    'page': page,
                    'limit': limit,
                    'total': total_count,
                    'total_pages': total_pages
                }
            }
        })
        
    except Exception as e:
        logger.error(f"Error getting workspace products: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@workspace_bp.route('/remove', methods=['POST'])
def remove_from_workspace():
    """Remove products from workspace"""
    try:
        data = request.get_json()
        
        if not data or 'product_ids' not in data:
            return jsonify({
                'success': False,
                'error': 'product_ids is required'
            }), 400
        
        product_ids = data['product_ids']
        
        # Update products to remove from workspace
        updated_count = 0
        errors = []
        
        for product_id in product_ids:
            try:
                # Update product status back to 'imported'
                update_data = {
                    'status': 'imported',
                    'workspace_added_at': None,
                    'updated_at': datetime.now(timezone.utc)
                }
                
                success = mongodb_service.update_product(product_id, update_data)
                if success:
                    updated_count += 1
                else:
                    errors.append(f"Failed to update product {product_id}")
            except Exception as e:
                errors.append(f"Error updating product {product_id}: {str(e)}")
        
        return jsonify({
            'success': True,
            'data': {
                'removed_count': updated_count,
                'total_requested': len(product_ids),
                'errors': errors
            }
        })
        
    except Exception as e:
        logger.error(f"Error removing products from workspace: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@workspace_bp.route('/stats', methods=['GET'])
def get_workspace_stats():
    """Get workspace statistics"""
    try:
        # Count products in workspace
        in_workspace_count = mongodb_service.db.products.count_documents({'status': 'in_workspace'})
        
        # Count products ready for Shopify
        ready_count = mongodb_service.db.products.count_documents({'status': 'shopify_ready'})
        
        # Count products being processed
        processing_count = mongodb_service.db.products.count_documents({'status': 'processing'})
        
        return jsonify({
            'success': True,
            'data': {
                'in_workspace': in_workspace_count,
                'shopify_ready': ready_count,
                'processing': processing_count,
                'total_workspace': in_workspace_count + ready_count + processing_count
            }
        })
        
    except Exception as e:
        logger.error(f"Error getting workspace stats: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@workspace_bp.route('/clear', methods=['POST'])
def clear_workspace():
    """Clear all products from workspace and remove their Cloudinary images"""
    try:
        # Get workspace products to extract their image public_ids before clearing
        workspace_products = list(mongodb_service.db.products.find(
            {'status': {'$in': ['in_workspace', 'processing', 'shopify_ready']}},
            {'images': 1, '_id': 1}
        ))

        # Collect all Cloudinary public_ids for deletion
        cloudinary_public_ids = []
        for product in workspace_products:
            images = product.get('images', [])
            for image in images:
                public_id = image.get('public_id')
                if public_id:
                    cloudinary_public_ids.append(public_id)

        # Delete images from Cloudinary
        cloudinary_result = {'deleted_count': 0, 'errors': []}
        if cloudinary_public_ids:
            logger.info(f"Deleting {len(cloudinary_public_ids)} images from Cloudinary during workspace clear")

            # Delete images in batches
            batch_size = 100
            for i in range(0, len(cloudinary_public_ids), batch_size):
                batch = cloudinary_public_ids[i:i + batch_size]
                try:
                    import cloudinary.api
                    delete_result = cloudinary.api.delete_resources(batch)

                    # Count successful deletions
                    for public_id, status in delete_result.get('deleted', {}).items():
                        if status == 'deleted':
                            cloudinary_result['deleted_count'] += 1
                        else:
                            cloudinary_result['errors'].append(f"Failed to delete {public_id}: {status}")

                except Exception as e:
                    error_msg = f"Error deleting batch {i//batch_size + 1}: {str(e)}"
                    cloudinary_result['errors'].append(error_msg)
                    logger.error(error_msg)

        # Update all workspace products back to imported status
        # But preserve original AliExpress images while removing Cloudinary images
        workspace_products_full = list(mongodb_service.db.products.find(
            {'status': {'$in': ['in_workspace', 'processing', 'shopify_ready']}},
            {'images': 1, 'original_content': 1, '_id': 1}
        ))

        updated_count = 0
        for product in workspace_products_full:
            try:
                # Preserve original AliExpress images from the current images array
                original_images = []
                current_images = product.get('images', [])

                for img in current_images:
                    # Keep only original AliExpress images (not Cloudinary processed ones)
                    img_url = img.get('url', '')
                    if (img_url and
                        img_url.startswith('https://ae01.alicdn.com') and
                        not img.get('cloudinary_url') and
                        not img.get('public_id')):

                        # Restore original image structure
                        original_images.append({
                            'url': img_url,
                            'local_path': '',
                            'processed_path': '',
                            'is_main': img.get('is_main', False),
                            'is_selected_by_user': img.get('is_selected_by_user', False),
                            'background_removal': {
                                'status': 'pending',
                                'api_provider': '',
                                'cost': 0
                            },
                            'download_status': 'pending'
                        })

                # Update the product
                update_data = {
                    'status': 'imported',
                    'workspace_added_at': None,
                    'updated_at': datetime.now(timezone.utc),
                    'images': original_images  # Keep only original AliExpress images
                }

                # Remove workspace-specific fields
                unset_data = {
                    'shopify_product_id': "",
                    'shopify_exported_at': ""
                }

                mongodb_service.db.products.update_one(
                    {'_id': product['_id']},
                    {
                        '$set': update_data,
                        '$unset': unset_data
                    }
                )
                updated_count += 1

            except Exception as e:
                logger.error(f"Error updating product {product['_id']}: {e}")

        # Create a result object similar to update_many
        class ClearResult:
            def __init__(self, count):
                self.modified_count = count

        result = ClearResult(updated_count)

        return jsonify({
            'success': True,
            'data': {
                'cleared_count': result.modified_count,
                'cloudinary_deleted': cloudinary_result['deleted_count'],
                'cloudinary_errors': cloudinary_result['errors'],
                'message': f'Cleared {result.modified_count} products and deleted {cloudinary_result["deleted_count"]} images from Cloudinary'
            }
        })

    except Exception as e:
        logger.error(f"Error clearing workspace: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@workspace_bp.route('/reset-statuses', methods=['POST'])
def reset_product_statuses():
    """Reset all product statuses to 'imported', clear workspace fields, and remove Cloudinary images"""
    try:
        # Get all products to extract their image public_ids before resetting
        products = list(mongodb_service.db.products.find({}, {'images': 1, '_id': 1}))

        # Collect all Cloudinary public_ids for deletion
        cloudinary_public_ids = []
        for product in products:
            images = product.get('images', [])
            for image in images:
                public_id = image.get('public_id')
                if public_id:
                    cloudinary_public_ids.append(public_id)

        # Delete images from Cloudinary
        cloudinary_result = {'deleted_count': 0, 'errors': []}
        if cloudinary_public_ids:
            logger.info(f"Deleting {len(cloudinary_public_ids)} images from Cloudinary during reset")

            # Delete images in batches to avoid API limits
            batch_size = 100
            for i in range(0, len(cloudinary_public_ids), batch_size):
                batch = cloudinary_public_ids[i:i + batch_size]
                try:
                    # Use Cloudinary's bulk delete API
                    import cloudinary.api
                    delete_result = cloudinary.api.delete_resources(batch)

                    # Count successful deletions
                    for public_id, status in delete_result.get('deleted', {}).items():
                        if status == 'deleted':
                            cloudinary_result['deleted_count'] += 1
                        else:
                            cloudinary_result['errors'].append(f"Failed to delete {public_id}: {status}")

                except Exception as e:
                    error_msg = f"Error deleting batch {i//batch_size + 1}: {str(e)}"
                    cloudinary_result['errors'].append(error_msg)
                    logger.error(error_msg)

        # Reset all products to 'imported' status and clear workspace fields
        # But preserve original AliExpress images while removing Cloudinary images
        all_products = list(mongodb_service.db.products.find({}, {'images': 1, 'original_content': 1, '_id': 1}))

        updated_count = 0
        for product in all_products:
            try:
                # Preserve original AliExpress images from the current images array
                original_images = []
                current_images = product.get('images', [])

                for img in current_images:
                    # Keep only original AliExpress images (not Cloudinary processed ones)
                    img_url = img.get('url', '')
                    if (img_url and
                        img_url.startswith('https://ae01.alicdn.com') and
                        not img.get('cloudinary_url') and
                        not img.get('public_id')):

                        # Restore original image structure
                        original_images.append({
                            'url': img_url,
                            'local_path': '',
                            'processed_path': '',
                            'is_main': img.get('is_main', False),
                            'is_selected_by_user': img.get('is_selected_by_user', False),
                            'background_removal': {
                                'status': 'pending',
                                'api_provider': '',
                                'cost': 0
                            },
                            'download_status': 'pending'
                        })

                # Update the product
                update_data = {
                    'status': 'imported',
                    'updated_at': datetime.now(timezone.utc),
                    'images': original_images  # Keep only original AliExpress images
                }

                # Remove workspace-specific fields
                unset_data = {
                    'workspace_added_at': "",
                    'shopify_product_id': "",
                    'shopify_exported_at': ""
                }

                mongodb_service.db.products.update_one(
                    {'_id': product['_id']},
                    {
                        '$set': update_data,
                        '$unset': unset_data
                    }
                )
                updated_count += 1

            except Exception as e:
                logger.error(f"Error updating product {product['_id']}: {e}")

        # Create a result object similar to update_many
        class ResetResult:
            def __init__(self, count):
                self.modified_count = count

        result = ResetResult(updated_count)

        return jsonify({
            'success': True,
            'data': {
                'reset_count': result.modified_count,
                'cloudinary_deleted': cloudinary_result['deleted_count'],
                'cloudinary_errors': cloudinary_result['errors'],
                'message': f'Reset {result.modified_count} products and deleted {cloudinary_result["deleted_count"]} images from Cloudinary'
            }
        })

    except Exception as e:
        logger.error(f"Error resetting product statuses: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@workspace_bp.route('/debug/statuses', methods=['GET'])
def debug_product_statuses():
    """Debug endpoint to see product statuses"""
    try:
        # Get all unique statuses
        pipeline = [
            {"$group": {"_id": "$status", "count": {"$sum": 1}}},
            {"$sort": {"count": -1}}
        ]

        status_counts = list(mongodb_service.db.products.aggregate(pipeline))

        # Get sample products for each status
        status_samples = {}
        for status_info in status_counts:
            status = status_info['_id']
            sample = mongodb_service.db.products.find_one(
                {"status": status},
                {"_id": 1, "title": 1, "status": 1, "workspace_added_at": 1, "created_at": 1}
            )
            if sample:
                sample['_id'] = str(sample['_id'])
                status_samples[status] = sample

        return jsonify({
            'success': True,
            'data': {
                'status_counts': status_counts,
                'status_samples': status_samples
            }
        })

    except Exception as e:
        logger.error(f"Error getting debug info: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500
