"""
Background Removal Service for processing product images
"""
import os
import requests
import logging
from typing import List, Dict, Optional
from datetime import datetime, timezone
from PIL import Image
import io

from config import Config
from services.mongodb_service import mongodb_service
from services.cloudinary_service import cloudinary_service

logger = logging.getLogger(__name__)

class BackgroundRemovalService:
    """Service for removing backgrounds from product images"""
    
    def __init__(self):
        self.config = Config()
        self.providers = {
            'remove.bg': {
                'api_key': self.config.REMOVE_BG_API_KEY,
                'endpoint': 'https://api.remove.bg/v1.0/removebg',
                'cost_per_image': 0.02
            },
            'photoroom': {
                'api_key': self.config.PHOTOROOM_API_KEY,
                'endpoint': 'https://api.photoroom.com/v1/segment',
                'cost_per_image': 0.015
            },
            'clipdrop': {
                'api_key': self.config.CLIPDROP_API_KEY,
                'endpoint': 'https://clipdrop-api.co/remove-background/v1',
                'cost_per_image': 0.01
            },
            'cloudinary': {
                'api_key': 'configured',  # Always available if Cloudinary is configured
                'endpoint': 'cloudinary_ai',
                'cost_per_image': 0.005  # Cloudinary AI is typically cheaper
            }
        }
    
    def remove_background_for_product(self, product_id: str, provider: str = 'remove.bg', image_indices: List[int] = None) -> Dict:
        """Remove background for specific product images"""
        try:
            # Get product
            product = mongodb_service.get_product(product_id)
            if not product:
                return {'success': False, 'error': 'Product not found'}
            
            images = product.get('images', [])
            if not images:
                return {'success': False, 'error': 'No images found for product'}
            
            # If no specific indices provided, process all images
            if image_indices is None:
                image_indices = list(range(len(images)))
            
            results = {
                'success': True,
                'processed_count': 0,
                'total_requested': len(image_indices),
                'errors': [],
                'total_cost': 0
            }
            
            for index in image_indices:
                if index >= len(images):
                    results['errors'].append(f'Invalid image index: {index}')
                    continue
                
                try:
                    # Process single image
                    image_result = self._process_single_image(
                        product_id, index, images[index], provider
                    )
                    
                    if image_result['success']:
                        results['processed_count'] += 1
                        results['total_cost'] += image_result.get('cost', 0)
                        
                        # Update image in product
                        images[index]['background_removal'] = {
                            'status': 'completed',
                            'api_provider': provider,
                            'processed_path': image_result['processed_path'],
                            'processing_timestamp': datetime.now(timezone.utc),
                            'cost': image_result.get('cost', 0)
                        }
                        images[index]['processed_path'] = image_result['processed_path']
                    else:
                        results['errors'].append(f'Image {index}: {image_result["error"]}')
                        images[index]['background_removal']['status'] = 'failed'
                
                except Exception as e:
                    error_msg = f'Error processing image {index}: {str(e)}'
                    results['errors'].append(error_msg)
                    logger.error(error_msg)
            
            # Update product with processed images
            mongodb_service.update_product(product_id, {'images': images})
            
            return results
            
        except Exception as e:
            logger.error(f"Error in background removal for product {product_id}: {e}")
            return {'success': False, 'error': str(e)}
    
    def bulk_remove_background(self, product_ids: List[str], provider: str = 'remove.bg') -> Dict:
        """Bulk remove background for multiple products"""
        results = {
            'success': True,
            'processed_products': 0,
            'total_requested': len(product_ids),
            'total_images_processed': 0,
            'total_cost': 0,
            'errors': []
        }
        
        try:
            for product_id in product_ids:
                try:
                    product_result = self.remove_background_for_product(product_id, provider)
                    
                    if product_result['success']:
                        results['processed_products'] += 1
                        results['total_images_processed'] += product_result['processed_count']
                        results['total_cost'] += product_result['total_cost']
                    
                    results['errors'].extend(product_result.get('errors', []))
                    
                except Exception as e:
                    error_msg = f'Error processing product {product_id}: {str(e)}'
                    results['errors'].append(error_msg)
                    logger.error(error_msg)
            
            return results
            
        except Exception as e:
            logger.error(f"Error in bulk background removal: {e}")
            results['success'] = False
            results['errors'].append(f'Bulk processing error: {str(e)}')
            return results
    
    def _process_single_image(self, product_id: str, image_index: int, image_data: Dict, provider: str) -> Dict:
        """Process a single image for background removal using Cloudinary URL"""
        try:
            # Get Cloudinary URL instead of local path
            cloudinary_url = image_data.get('cloudinary_url', '')
            if not cloudinary_url:
                return {'success': False, 'error': 'No Cloudinary URL found for image'}

            # Process based on provider using Cloudinary URL
            if provider == 'remove.bg':
                result = self._process_with_removebg_url(cloudinary_url)
            elif provider == 'photoroom':
                result = self._process_with_photoroom_url(cloudinary_url)
            elif provider == 'clipdrop':
                result = self._process_with_clipdrop_url(cloudinary_url)
            elif provider == 'cloudinary':
                result = self._process_with_cloudinary(image_data, product_id, image_index)
            else:
                return {'success': False, 'error': f'Unsupported provider: {provider}'}

            if result['success']:
                # For Cloudinary, the result already contains the processed image URL
                if provider == 'cloudinary':
                    return result
                else:
                    # For other providers, upload processed image back to Cloudinary
                    processed_cloudinary_result = self._upload_processed_to_cloudinary(
                        product_id, image_index, result['image_data']
                    )

                    if processed_cloudinary_result['success']:
                        return {
                            'success': True,
                            'processed_cloudinary_url': processed_cloudinary_result['cloudinary_url'],
                            'processed_public_id': processed_cloudinary_result['public_id'],
                            'cost': self.providers[provider]['cost_per_image']
                        }
                    else:
                        return {'success': False, 'error': f'Failed to upload processed image: {processed_cloudinary_result["error"]}'}
            else:
                return result

        except Exception as e:
            logger.error(f"Error processing single image: {e}")
            return {'success': False, 'error': str(e)}
    
    def _process_with_removebg(self, image_path: str) -> Dict:
        """Process image with Remove.bg API"""
        try:
            if not self.providers['remove.bg']['api_key']:
                return {'success': False, 'error': 'Remove.bg API key not configured'}
            
            with open(image_path, 'rb') as image_file:
                response = requests.post(
                    self.providers['remove.bg']['endpoint'],
                    files={'image_file': image_file},
                    data={'size': 'auto'},
                    headers={'X-Api-Key': self.providers['remove.bg']['api_key']},
                    timeout=30
                )
            
            if response.status_code == 200:
                return {
                    'success': True,
                    'image_data': response.content
                }
            else:
                error_msg = f"Remove.bg API error: {response.status_code} - {response.text}"
                logger.error(error_msg)
                return {'success': False, 'error': error_msg}
                
        except Exception as e:
            logger.error(f"Error with Remove.bg processing: {e}")
            return {'success': False, 'error': str(e)}
    
    def _process_with_photoroom(self, image_path: str) -> Dict:
        """Process image with PhotoRoom API"""
        try:
            if not self.providers['photoroom']['api_key']:
                return {'success': False, 'error': 'PhotoRoom API key not configured'}
            
            with open(image_path, 'rb') as image_file:
                response = requests.post(
                    self.providers['photoroom']['endpoint'],
                    files={'image_file': image_file},
                    headers={'x-api-key': self.providers['photoroom']['api_key']},
                    timeout=30
                )
            
            if response.status_code == 200:
                return {
                    'success': True,
                    'image_data': response.content
                }
            else:
                error_msg = f"PhotoRoom API error: {response.status_code} - {response.text}"
                logger.error(error_msg)
                return {'success': False, 'error': error_msg}
                
        except Exception as e:
            logger.error(f"Error with PhotoRoom processing: {e}")
            return {'success': False, 'error': str(e)}
    
    def _process_with_clipdrop(self, image_path: str) -> Dict:
        """Process image with ClipDrop API"""
        try:
            if not self.providers['clipdrop']['api_key']:
                return {'success': False, 'error': 'ClipDrop API key not configured'}
            
            with open(image_path, 'rb') as image_file:
                response = requests.post(
                    self.providers['clipdrop']['endpoint'],
                    files={'image_file': image_file},
                    headers={'x-api-key': self.providers['clipdrop']['api_key']},
                    timeout=30
                )
            
            if response.status_code == 200:
                return {
                    'success': True,
                    'image_data': response.content
                }
            else:
                error_msg = f"ClipDrop API error: {response.status_code} - {response.text}"
                logger.error(error_msg)
                return {'success': False, 'error': error_msg}
                
        except Exception as e:
            logger.error(f"Error with ClipDrop processing: {e}")
            return {'success': False, 'error': str(e)}
    
    def _save_processed_image(self, product_id: str, image_index: int, image_data: bytes) -> str:
        """Save processed image to disk"""
        try:
            # Create processed images directory
            processed_dir = os.path.join(self.config.PROCESSED_IMAGE_PATH, product_id)
            os.makedirs(processed_dir, exist_ok=True)
            
            # Generate filename
            filename = f"processed_{image_index:03d}.png"
            file_path = os.path.join(processed_dir, filename)
            
            # Save image
            with open(file_path, 'wb') as f:
                f.write(image_data)
            
            logger.info(f"Saved processed image: {file_path}")
            return file_path
            
        except Exception as e:
            logger.error(f"Error saving processed image: {e}")
            raise
    
    def retry_failed_processing(self, product_id: str) -> Dict:
        """Retry failed background removal processing"""
        try:
            # Get product
            product = mongodb_service.get_product(product_id)
            if not product:
                return {'success': False, 'error': 'Product not found'}
            
            images = product.get('images', [])
            failed_indices = []
            
            # Find failed images
            for i, image in enumerate(images):
                bg_status = image.get('background_removal', {}).get('status', 'pending')
                if bg_status == 'failed':
                    failed_indices.append(i)
            
            if not failed_indices:
                return {
                    'success': True,
                    'message': 'No failed images to retry',
                    'processed_count': 0
                }
            
            # Retry processing
            provider = self.config.DEFAULT_BG_REMOVAL_PROVIDER
            return self.remove_background_for_product(product_id, provider, failed_indices)
            
        except Exception as e:
            logger.error(f"Error retrying failed processing: {e}")
            return {'success': False, 'error': str(e)}
    
    def estimate_cost(self, image_count: int, provider: str) -> float:
        """Estimate cost for processing images"""
        try:
            if provider not in self.providers:
                return 0.0
            
            cost_per_image = self.providers[provider]['cost_per_image']
            return round(image_count * cost_per_image, 2)
            
        except Exception as e:
            logger.error(f"Error estimating cost: {e}")
            return 0.0
    
    def get_available_providers(self) -> Dict:
        """Get available background removal providers"""
        providers_info = {}
        
        for provider_name, provider_config in self.providers.items():
            providers_info[provider_name] = {
                'name': provider_name.replace('.', ' ').title(),
                'available': bool(provider_config['api_key']),
                'cost_per_image': provider_config['cost_per_image'],
                'endpoint': provider_config['endpoint']
            }
        
        return providers_info
    
    def get_processing_stats(self) -> Dict:
        """Get background removal processing statistics"""
        try:
            # Get stats from database
            pipeline = [
                {'$unwind': '$images'},
                {'$group': {
                    '_id': '$images.background_removal.status',
                    'count': {'$sum': 1},
                    'total_cost': {'$sum': '$images.background_removal.cost'}
                }}
            ]
            
            results = list(mongodb_service.db.products.aggregate(pipeline))
            
            stats = {
                'total_images': 0,
                'completed': 0,
                'failed': 0,
                'pending': 0,
                'processing': 0,
                'total_cost': 0
            }
            
            for result in results:
                status = result['_id'] or 'pending'
                count = result['count']
                cost = result.get('total_cost', 0)
                
                stats['total_images'] += count
                stats[status] = count
                stats['total_cost'] += cost
            
            stats['total_cost'] = round(stats['total_cost'], 2)
            
            return stats

        except Exception as e:
            logger.error(f"Error getting processing stats: {e}")
            return {}

    def _process_with_removebg_url(self, image_url: str) -> Dict:
        """Process image with Remove.bg API using URL"""
        try:
            if not self.providers['remove.bg']['api_key']:
                return {'success': False, 'error': 'Remove.bg API key not configured'}

            response = requests.post(
                self.providers['remove.bg']['endpoint'],
                data={'image_url': image_url, 'size': 'auto'},
                headers={'X-Api-Key': self.providers['remove.bg']['api_key']},
                timeout=30
            )

            if response.status_code == 200:
                return {
                    'success': True,
                    'image_data': response.content
                }
            else:
                error_msg = f"Remove.bg API error: {response.status_code} - {response.text}"
                logger.error(error_msg)
                return {'success': False, 'error': error_msg}

        except Exception as e:
            logger.error(f"Error processing with Remove.bg URL: {e}")
            return {'success': False, 'error': str(e)}

    def _process_with_photoroom_url(self, image_url: str) -> Dict:
        """Process image with PhotoRoom API using URL"""
        try:
            if not self.providers['photoroom']['api_key']:
                return {'success': False, 'error': 'PhotoRoom API key not configured'}

            response = requests.post(
                self.providers['photoroom']['endpoint'],
                data={'image_url': image_url},
                headers={'x-api-key': self.providers['photoroom']['api_key']},
                timeout=30
            )

            if response.status_code == 200:
                return {
                    'success': True,
                    'image_data': response.content
                }
            else:
                error_msg = f"PhotoRoom API error: {response.status_code} - {response.text}"
                logger.error(error_msg)
                return {'success': False, 'error': error_msg}

        except Exception as e:
            logger.error(f"Error processing with PhotoRoom URL: {e}")
            return {'success': False, 'error': str(e)}

    def _process_with_clipdrop_url(self, image_url: str) -> Dict:
        """Process image with ClipDrop API using URL"""
        try:
            if not self.providers['clipdrop']['api_key']:
                return {'success': False, 'error': 'ClipDrop API key not configured'}

            response = requests.post(
                self.providers['clipdrop']['endpoint'],
                data={'image_url': image_url},
                headers={'x-api-key': self.providers['clipdrop']['api_key']},
                timeout=30
            )

            if response.status_code == 200:
                return {
                    'success': True,
                    'image_data': response.content
                }
            else:
                error_msg = f"ClipDrop API error: {response.status_code} - {response.text}"
                logger.error(error_msg)
                return {'success': False, 'error': error_msg}

        except Exception as e:
            logger.error(f"Error processing with ClipDrop URL: {e}")
            return {'success': False, 'error': str(e)}

    def _upload_processed_to_cloudinary(self, product_id: str, image_index: int, image_data: bytes) -> Dict:
        """Upload processed image to Cloudinary"""
        try:
            import tempfile
            import os

            # Create temporary file for the processed image
            with tempfile.NamedTemporaryFile(delete=False, suffix='.png') as temp_file:
                temp_file.write(image_data)
                temp_path = temp_file.name

            try:
                # Upload to Cloudinary
                public_id = f"{product_id}/processed_image_{image_index}"
                upload_result = cloudinary_service.upload_image(
                    image_path=temp_path,
                    public_id=public_id,
                    folder=f"products/{product_id}/processed"
                )

                return upload_result

            finally:
                # Clean up temporary file
                try:
                    os.unlink(temp_path)
                except Exception as e:
                    logger.warning(f"Could not remove temp file {temp_path}: {e}")

        except Exception as e:
            logger.error(f"Error uploading processed image to Cloudinary: {e}")
            return {'success': False, 'error': str(e)}

    def _process_with_cloudinary(self, image_data: Dict, product_id: str, image_index: int) -> Dict:
        """Process image with Cloudinary's AI background removal"""
        try:
            public_id = image_data.get('public_id', '')
            if not public_id:
                return {'success': False, 'error': 'No public_id found for Cloudinary processing'}

            # Create new public_id for processed image
            processed_public_id = f"{product_id}/bg_removed_image_{image_index}"

            # Use Cloudinary's background removal
            result = cloudinary_service.remove_background(public_id, processed_public_id)

            if result['success']:
                return {
                    'success': True,
                    'processed_cloudinary_url': result['cloudinary_url'],
                    'processed_public_id': result['public_id'],
                    'cost': self.providers['cloudinary']['cost_per_image']
                }
            else:
                return result

        except Exception as e:
            logger.error(f"Error processing with Cloudinary AI: {e}")
            return {'success': False, 'error': str(e)}