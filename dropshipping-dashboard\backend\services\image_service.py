"""
Image Service for integrating with AliExpress image downloader
"""
import os
import sys
import logging
from typing import List, Dict, Optional
from datetime import datetime, timezone
import subprocess
import shutil
import threading
import time
import concurrent.futures

# Add the parent directory to path to import the existing downloader
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(__file__))))

from config import Config
from services.mongodb_service import mongodb_service
from services.cloudinary_service import cloudinary_service

logger = logging.getLogger(__name__)

class ImageService:
    """Service for extracting and managing product images"""
    
    def __init__(self):
        self.config = Config()
        # Path to the existing AliExpress image downloader
        self.downloader_path = os.path.join(
            os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(__file__)))),
            'aliexpress_image_downloader.py'
        )
    
    def extract_product_images(self, product_id: str) -> Dict:
        """Extract images for a single product"""
        try:
            # Get product
            product = mongodb_service.get_product(product_id)
            if not product:
                return {'success': False, 'error': 'Product not found'}
            
            # Get AliExpress URL
            aliexpress_url = product.get('aliexpress_data', {}).get('original_url', '')
            if not aliexpress_url:
                return {'success': False, 'error': 'No AliExpress URL found for product'}

            # Fix protocol-relative URLs - remove // and add https://
            if aliexpress_url.startswith('//'):
                # Remove the // and add https://
                aliexpress_url = 'https://' + aliexpress_url[2:]
            elif not aliexpress_url.startswith('http'):
                aliexpress_url = 'https://' + aliexpress_url

            logger.info(f"Fixed URL: {aliexpress_url}")
            
            # Update product status
            mongodb_service.update_product(product_id, {
                'status': 'processing',
                'image_extraction_started': datetime.now(timezone.utc)
            })
            
            # Create product-specific download directory
            download_dir = os.path.join(self.config.IMAGE_STORAGE_PATH, product_id)
            os.makedirs(download_dir, exist_ok=True)
            
            # Use the existing AliExpress image downloader
            result = self._run_aliexpress_downloader(aliexpress_url, download_dir)
            
            if result['success']:
                # Update product with downloaded images
                image_files = result['image_files']
                images_data = []
                
                for i, image_file in enumerate(image_files):
                    # Upload image to Cloudinary instead of using local paths
                    try:
                        # Create a unique public_id for the image
                        filename = os.path.basename(image_file)
                        public_id = f"{product_id}/image_{i}_{filename.split('.')[0]}"
                        
                        # Upload to Cloudinary
                        upload_result = cloudinary_service.upload_image(
                            image_path=image_file,
                            public_id=public_id,
                            folder=f"products/{product_id}"
                        )
                        
                        if upload_result['success']:
                            cloudinary_url = upload_result['cloudinary_url']
                            public_id_used = upload_result['public_id']
                            logger.info(f"Uploaded image to Cloudinary: {cloudinary_url}")
                            
                            # Remove local file after successful upload
                            try:
                                os.remove(image_file)
                                logger.info(f"Removed local file: {image_file}")
                            except Exception as e:
                                logger.warning(f"Could not remove local file {image_file}: {e}")
                        else:
                            # Skip image if Cloudinary upload fails - no local fallback
                            logger.error(f"Cloudinary upload failed: {upload_result['error']} - Skipping image")
                            continue
                            
                    except Exception as e:
                        logger.error(f"Error processing image {image_file}: {e}")
                        # Skip image if processing fails - no local fallback
                        continue

                    images_data.append({
                        'url': cloudinary_url,
                        'cloudinary_url': cloudinary_url,
                        'public_id': public_id_used,
                        'local_path': '',  # No longer storing local paths
                        'processed_path': '',
                        'is_main': i == 0,
                        'is_selected_by_user': i == 0,
                        'background_removal': {
                            'status': 'pending',
                            'api_provider': '',
                            'cost': 0
                        },
                        'download_status': 'completed'
                    })
                
                # Update product
                update_data = {
                    'images': images_data,
                    'status': 'ready',
                    'image_extraction_completed': datetime.now(timezone.utc),
                    'total_images': len(images_data)
                }
                
                mongodb_service.update_product(product_id, update_data)
                
                return {
                    'success': True,
                    'images_extracted': len(images_data),
                    'download_directory': download_dir,
                    'images': images_data
                }
            else:
                # Update product status to failed
                mongodb_service.update_product(product_id, {
                    'status': 'failed',
                    'image_extraction_error': result['error']
                })
                
                return result
                
        except Exception as e:
            logger.error(f"Error extracting images for product {product_id}: {e}")
            mongodb_service.update_product(product_id, {
                'status': 'failed',
                'image_extraction_error': str(e)
            })
            return {'success': False, 'error': str(e)}
    
    def bulk_extract_images(self, product_ids: List[str], max_concurrent: int = 3) -> Dict:
        """
        Extract images for multiple products with concurrency control

        Args:
            product_ids: List of product IDs to process
            max_concurrent: Maximum number of concurrent downloads (default: 3)
        """
        results = {
            'success': True,
            'processed_count': 0,
            'total_requested': len(product_ids),
            'total_images_extracted': 0,
            'errors': []
        }

        if len(product_ids) > 50:
            logger.warning(f"Large bulk request: {len(product_ids)} products. Consider processing in smaller batches.")

        try:
            # Use ThreadPoolExecutor for controlled concurrency
            with concurrent.futures.ThreadPoolExecutor(max_workers=max_concurrent) as executor:
                # Submit all tasks
                future_to_product = {
                    executor.submit(self._safe_extract_product_images, product_id): product_id
                    for product_id in product_ids
                }

                # Process completed tasks
                for future in concurrent.futures.as_completed(future_to_product, timeout=300):  # 5 min timeout
                    product_id = future_to_product[future]
                    try:
                        product_result = future.result(timeout=60)  # 1 min per product

                        if product_result['success']:
                            results['processed_count'] += 1
                            results['total_images_extracted'] += product_result.get('images_extracted', 0)
                        else:
                            results['errors'].append(f"Product {product_id}: {product_result['error']}")

                    except concurrent.futures.TimeoutError:
                        error_msg = f"Product {product_id}: Timeout after 60 seconds"
                        results['errors'].append(error_msg)
                        logger.error(error_msg)
                    except Exception as e:
                        error_msg = f"Product {product_id}: {str(e)}"
                        results['errors'].append(error_msg)
                        logger.error(error_msg)

            return results

        except Exception as e:
            logger.error(f"Error in bulk image extraction: {e}")
            results['success'] = False
            results['errors'].append(f'Bulk processing error: {str(e)}')
            return results

    def _safe_extract_product_images(self, product_id: str) -> Dict:
        """Thread-safe wrapper for extract_product_images with additional error handling"""
        try:
            # Add small random delay to prevent overwhelming AliExpress
            time.sleep(0.5 + (hash(product_id) % 10) * 0.1)  # 0.5-1.5 second delay
            return self.extract_product_images(product_id)
        except Exception as e:
            logger.error(f"Error in safe extract for product {product_id}: {e}")
            return {'success': False, 'error': str(e)}
    
    def _run_aliexpress_downloader(self, url: str, download_dir: str) -> Dict:
        """Run the existing AliExpress image downloader"""
        try:
            logger.info(f"Looking for downloader at: {self.downloader_path}")
            logger.info(f"Downloader exists: {os.path.exists(self.downloader_path)}")

            if not os.path.exists(self.downloader_path):
                return {
                    'success': False,
                    'error': f'AliExpress image downloader not found at: {self.downloader_path}'
                }
            
            # Prepare command
            cmd = [
                sys.executable,
                self.downloader_path,
                '-u', url,
                '-d', download_dir,
                '-w', str(self.config.MAX_CONCURRENT_DOWNLOADS)
            ]
            
            # Run the downloader
            logger.info(f"Running AliExpress downloader: {' '.join(cmd)}")
            
            result = subprocess.run(
                cmd,
                capture_output=True,
                text=True,
                timeout=300  # 5 minute timeout
            )
            
            if result.returncode == 0:
                # Parse the downloaded files
                image_files = self._find_downloaded_images(download_dir)
                
                if image_files:
                    return {
                        'success': True,
                        'image_files': image_files,
                        'stdout': result.stdout,
                        'stderr': result.stderr
                    }
                else:
                    return {
                        'success': False,
                        'error': 'No images were downloaded',
                        'stdout': result.stdout,
                        'stderr': result.stderr
                    }
            else:
                return {
                    'success': False,
                    'error': f'Downloader failed with code {result.returncode}',
                    'stdout': result.stdout,
                    'stderr': result.stderr
                }
                
        except subprocess.TimeoutExpired:
            return {
                'success': False,
                'error': 'Download timeout (5 minutes exceeded)'
            }
        except Exception as e:
            logger.error(f"Error running AliExpress downloader: {e}")
            return {'success': False, 'error': str(e)}
    
    def _find_downloaded_images(self, download_dir: str) -> List[str]:
        """Find downloaded image files in the directory"""
        try:
            image_files = []
            image_extensions = {'.jpg', '.jpeg', '.png', '.webp', '.gif'}
            
            # Look for product directories created by the downloader
            for item in os.listdir(download_dir):
                item_path = os.path.join(download_dir, item)
                
                if os.path.isdir(item_path):
                    # Look for images in subdirectory
                    for file in os.listdir(item_path):
                        file_path = os.path.join(item_path, file)
                        if os.path.isfile(file_path):
                            _, ext = os.path.splitext(file.lower())
                            if ext in image_extensions:
                                image_files.append(file_path)
                elif os.path.isfile(item_path):
                    # Direct image file
                    _, ext = os.path.splitext(item.lower())
                    if ext in image_extensions:
                        image_files.append(item_path)
            
            # Sort files for consistent ordering
            image_files.sort()
            return image_files
            
        except Exception as e:
            logger.error(f"Error finding downloaded images: {e}")
            return []
    
    def retry_failed_downloads(self, product_id: str) -> Dict:
        """Retry failed image downloads"""
        try:
            # Get product
            product = mongodb_service.get_product(product_id)
            if not product:
                return {'success': False, 'error': 'Product not found'}
            
            # Check if product has failed status
            if product.get('status') != 'failed':
                return {
                    'success': True,
                    'message': 'Product does not have failed status',
                    'current_status': product.get('status')
                }
            
            # Retry extraction
            return self.extract_product_images(product_id)
            
        except Exception as e:
            logger.error(f"Error retrying failed downloads: {e}")
            return {'success': False, 'error': str(e)}
    
    def get_image_info(self, product_id: str, image_index: int) -> Dict:
        """Get detailed information about a specific image"""
        try:
            # Get product
            product = mongodb_service.get_product(product_id)
            if not product:
                return {'success': False, 'error': 'Product not found'}
            
            images = product.get('images', [])
            if image_index >= len(images):
                return {'success': False, 'error': 'Invalid image index'}
            
            image = images[image_index]
            image_info = {
                'index': image_index,
                'url': image.get('url', ''),
                'cloudinary_url': image.get('cloudinary_url', ''),
                'public_id': image.get('public_id', ''),
                'local_path': image.get('local_path', ''),  # Keep for backward compatibility
                'processed_path': image.get('processed_path', ''),
                'is_main': image.get('is_main', False),
                'is_selected_by_user': image.get('is_selected_by_user', False),
                'download_status': image.get('download_status', 'pending'),
                'background_removal': image.get('background_removal', {}),
                'file_exists': False,
                'file_size': 0,
                'dimensions': None
            }
            
            # Check image info from Cloudinary only - no local fallback
            public_id = image.get('public_id', '')

            # Get info from Cloudinary
            if public_id:
                try:
                    cloudinary_info = cloudinary_service.get_image_info(public_id)
                    if cloudinary_info['success']:
                        data = cloudinary_info['data']
                        image_info['file_exists'] = True
                        image_info['file_size'] = data.get('bytes', 0)
                        image_info['dimensions'] = {
                            'width': data.get('width', 0),
                            'height': data.get('height', 0)
                        }
                except Exception as e:
                    logger.warning(f"Could not get Cloudinary image info: {e}")
                    image_info['file_exists'] = False
            else:
                # No public_id means image not in Cloudinary
                image_info['file_exists'] = False
            
            return {
                'success': True,
                'data': image_info
            }
            
        except Exception as e:
            logger.error(f"Error getting image info: {e}")
            return {'success': False, 'error': str(e)}
    
    def delete_product_images(self, product_id: str) -> Dict:
        """Delete all images for a product from both Cloudinary and local storage"""
        try:
            # Get product
            product = mongodb_service.get_product(product_id)
            if not product:
                return {'success': False, 'error': 'Product not found'}
            
            # Delete image files
            deleted_files = 0
            errors = []
            
            images = product.get('images', [])
            for image in images:
                # Delete from Cloudinary first
                public_id = image.get('public_id', '')
                cloudinary_url = image.get('cloudinary_url', '')
                
                if public_id:
                    # Delete using public_id
                    delete_result = cloudinary_service.delete_image(public_id)
                    if delete_result['success']:
                        deleted_files += 1
                        logger.info(f"Deleted from Cloudinary: {public_id}")
                    else:
                        errors.append(f"Failed to delete from Cloudinary {public_id}: {delete_result['error']}")
                elif cloudinary_url and 'cloudinary' in cloudinary_url:
                    # Extract public_id from URL and delete
                    public_id = cloudinary_service.extract_public_id_from_url(cloudinary_url)
                    delete_result = cloudinary_service.delete_image(public_id)
                    if delete_result['success']:
                        deleted_files += 1
                        logger.info(f"Deleted from Cloudinary via URL: {public_id}")
                    else:
                        errors.append(f"Failed to delete from Cloudinary via URL {public_id}: {delete_result['error']}")
                
                # Delete local files (for backward compatibility with existing data)
                local_path = image.get('local_path', '')
                if local_path and os.path.exists(local_path):
                    try:
                        os.remove(local_path)
                        deleted_files += 1
                        logger.info(f"Deleted local file: {local_path}")
                    except Exception as e:
                        errors.append(f"Failed to delete local file {local_path}: {str(e)}")
                
                # Delete processed image
                processed_path = image.get('processed_path', '')
                if processed_path and os.path.exists(processed_path):
                    try:
                        os.remove(processed_path)
                        deleted_files += 1
                        logger.info(f"Deleted processed file: {processed_path}")
                    except Exception as e:
                        errors.append(f"Failed to delete processed file {processed_path}: {str(e)}")
            
            # Delete product directory if it exists and is empty
            product_dir = os.path.join(self.config.IMAGE_STORAGE_PATH, product_id)
            if os.path.exists(product_dir):
                try:
                    # Remove directory and all contents
                    shutil.rmtree(product_dir)
                    logger.info(f"Deleted product directory: {product_dir}")
                except Exception as e:
                    errors.append(f"Failed to delete directory {product_dir}: {str(e)}")
            
            # Update product to remove image references
            mongodb_service.update_product(product_id, {
                'images': [],
                'status': 'pending',
                'total_images': 0
            })
            
            return {
                'success': True,
                'deleted_files': deleted_files,
                'errors': errors
            }
            
        except Exception as e:
            logger.error(f"Error deleting product images: {e}")
            return {'success': False, 'error': str(e)}
    
    def get_extraction_stats(self) -> Dict:
        """Get image extraction statistics"""
        try:
            stats = {
                'total_products': mongodb_service.db.products.count_documents({}),
                'products_with_images': mongodb_service.db.products.count_documents({
                    'images': {'$exists': True, '$ne': []}
                }),
                'by_status': {},
                'total_images': 0,
                'average_images_per_product': 0
            }
            
            # Get status distribution
            status_pipeline = [
                {'$group': {'_id': '$status', 'count': {'$sum': 1}}}
            ]
            status_results = list(mongodb_service.db.products.aggregate(status_pipeline))
            for result in status_results:
                stats['by_status'][result['_id']] = result['count']
            
            # Get total images count
            image_pipeline = [
                {'$group': {
                    '_id': None,
                    'total_images': {'$sum': {'$size': {'$ifNull': ['$images', []]}}},
                    'products_with_images': {'$sum': {'$cond': [
                        {'$gt': [{'$size': {'$ifNull': ['$images', []]}}, 0]},
                        1,
                        0
                    ]}}
                }}
            ]
            
            image_results = list(mongodb_service.db.products.aggregate(image_pipeline))
            if image_results:
                result = image_results[0]
                stats['total_images'] = result.get('total_images', 0)
                products_with_images = result.get('products_with_images', 0)
                
                if products_with_images > 0:
                    stats['average_images_per_product'] = round(
                        stats['total_images'] / products_with_images, 2
                    )
            
            return stats
            
        except Exception as e:
            logger.error(f"Error getting extraction stats: {e}")
            return {}