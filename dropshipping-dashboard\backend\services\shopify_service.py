"""
Shopify Integration Service for exporting products to Shopify store
"""
import requests
import logging
from typing import Dict, List, Optional
from datetime import datetime, timezone
import base64
import io
from PIL import Image

from config import Config
from services.mongodb_service import mongodb_service

logger = logging.getLogger(__name__)

class ShopifyService:
    """Service for integrating with Shopify API"""
    
    def __init__(self):
        self.config = Config()
        self.api_key = self.config.SHOPIFY_API_KEY
        self.api_secret = self.config.SHOPIFY_API_SECRET
        self.store_url = self.config.SHOPIFY_STORE_URL
        self.base_url = f"https://{self.store_url}/admin/api/2023-10/products.json"
        self.headers = {
            'Content-Type': 'application/json',
            'X-Shopify-Access-Token': self.api_secret
        }
        
        if not all([self.api_key, self.api_secret, self.store_url]):
            logger.warning("Shopify credentials not fully configured")
    
    def test_connection(self) -> Dict:
        """Test connection to Shopify API"""
        try:
            if not all([self.api_key, self.api_secret, self.store_url]):
                return {
                    'success': False,
                    'error': 'Shopify credentials not configured'
                }
            
            # Test with a simple shop info request
            test_url = f"https://{self.store_url}/admin/api/2023-10/shop.json"
            response = requests.get(test_url, headers=self.headers, timeout=10)
            
            if response.status_code == 200:
                shop_data = response.json()
                return {
                    'success': True,
                    'shop_info': shop_data.get('shop', {}),
                    'message': 'Successfully connected to Shopify'
                }
            else:
                return {
                    'success': False,
                    'error': f'Shopify API error: {response.status_code} - {response.text}'
                }
                
        except Exception as e:
            logger.error(f"Error testing Shopify connection: {e}")
            return {
                'success': False,
                'error': str(e)
            }
    
    def export_product(self, product_id: str, selected_images: List[int] = None, main_image_index: int = 0) -> Dict:
        """Export a single product to Shopify"""
        try:
            # Get product from database
            product = mongodb_service.get_product(product_id)
            if not product:
                return {
                    'success': False,
                    'error': 'Product not found'
                }
            
            # Prepare product data for Shopify
            shopify_product = self._prepare_product_data(product, selected_images, main_image_index)
            
            # Create product in Shopify
            response = requests.post(
                self.base_url,
                json={'product': shopify_product},
                headers=self.headers,
                timeout=30
            )
            
            if response.status_code == 201:
                shopify_data = response.json()
                shopify_product_id = shopify_data['product']['id']
                
                # Update product in database with Shopify info
                mongodb_service.update_product(product_id, {
                    'shopify_product_id': shopify_product_id,
                    'shopify_exported_at': datetime.now(timezone.utc),
                    'status': 'exported_to_shopify',
                    'updated_at': datetime.now(timezone.utc)
                })
                
                return {
                    'success': True,
                    'shopify_product_id': shopify_product_id,
                    'shopify_url': f"https://{self.store_url}/admin/products/{shopify_product_id}",
                    'message': 'Product exported to Shopify successfully'
                }
            else:
                error_msg = f"Shopify API error: {response.status_code} - {response.text}"
                logger.error(error_msg)
                return {
                    'success': False,
                    'error': error_msg
                }
                
        except Exception as e:
            logger.error(f"Error exporting product {product_id} to Shopify: {e}")
            return {
                'success': False,
                'error': str(e)
            }
    
    def bulk_export_products(self, product_ids: List[str]) -> Dict:
        """Export multiple products to Shopify"""
        results = {
            'success': True,
            'exported_count': 0,
            'total_requested': len(product_ids),
            'errors': [],
            'exported_products': []
        }
        
        for product_id in product_ids:
            try:
                result = self.export_product(product_id)
                if result['success']:
                    results['exported_count'] += 1
                    results['exported_products'].append({
                        'product_id': product_id,
                        'shopify_product_id': result['shopify_product_id']
                    })
                else:
                    results['errors'].append(f"Product {product_id}: {result['error']}")
            except Exception as e:
                results['errors'].append(f"Product {product_id}: {str(e)}")
        
        if results['errors']:
            results['success'] = results['exported_count'] > 0
        
        return results
    
    def _prepare_product_data(self, product: Dict, selected_images: List[int] = None, main_image_index: int = 0) -> Dict:
        """Prepare product data for Shopify API"""
        
        # Get product images
        images = product.get('images', [])
        if selected_images is not None:
            # Filter to only selected images
            images = [images[i] for i in selected_images if i < len(images)]
        
        # Prepare images for Shopify
        shopify_images = []
        for i, image in enumerate(images):
            image_data = {
                'src': image.get('cloudinary_url', image.get('url', '')),
                'alt': f"{product.get('title', 'Product')} - Image {i+1}"
            }
            
            # Mark main image
            if i == main_image_index:
                image_data['position'] = 1
            
            shopify_images.append(image_data)
        
        # Calculate price with markup if available
        original_price = float(product.get('original_price', 0))
        final_price = float(product.get('final_price', original_price))
        
        # Prepare product data
        shopify_product = {
            'title': product.get('title', 'Untitled Product'),
            'body_html': product.get('description', ''),
            'vendor': 'R.A.V.E',
            'product_type': product.get('category', 'General'),
            'status': 'draft',  # Start as draft for review
            'images': shopify_images,
            'variants': [{
                'price': str(final_price),
                'compare_at_price': str(original_price) if original_price > final_price else None,
                'inventory_management': 'shopify',
                'inventory_quantity': 100,  # Default inventory
                'requires_shipping': True,
                'taxable': True
            }],
            'tags': [
                'imported',
                'aliexpress',
                product.get('currency', 'USD').upper()
            ]
        }
        
        return shopify_product
    
    def update_product_images(self, shopify_product_id: str, product_id: str, selected_images: List[int], main_image_index: int = 0) -> Dict:
        """Update images for an existing Shopify product"""
        try:
            # Get product from database
            product = mongodb_service.get_product(product_id)
            if not product:
                return {
                    'success': False,
                    'error': 'Product not found'
                }
            
            # Get product images
            images = product.get('images', [])
            selected_image_data = [images[i] for i in selected_images if i < len(images)]
            
            # Prepare images for Shopify
            shopify_images = []
            for i, image in enumerate(selected_image_data):
                image_data = {
                    'src': image.get('cloudinary_url', image.get('url', '')),
                    'alt': f"{product.get('title', 'Product')} - Image {i+1}"
                }
                
                # Mark main image
                if i == main_image_index:
                    image_data['position'] = 1
                
                shopify_images.append(image_data)
            
            # Update product in Shopify
            update_url = f"https://{self.store_url}/admin/api/2023-10/products/{shopify_product_id}.json"
            update_data = {
                'product': {
                    'id': shopify_product_id,
                    'images': shopify_images
                }
            }
            
            response = requests.put(
                update_url,
                json=update_data,
                headers=self.headers,
                timeout=30
            )
            
            if response.status_code == 200:
                return {
                    'success': True,
                    'message': 'Product images updated successfully'
                }
            else:
                error_msg = f"Shopify API error: {response.status_code} - {response.text}"
                logger.error(error_msg)
                return {
                    'success': False,
                    'error': error_msg
                }
                
        except Exception as e:
            logger.error(f"Error updating Shopify product images: {e}")
            return {
                'success': False,
                'error': str(e)
            }

# Create global instance
shopify_service = ShopifyService()
