/**
 * Shopify Workspace JavaScript
 * Handles displaying selected products with extracted images
 */

document.addEventListener('DOMContentLoaded', async () => {
    // Ensure app is loaded
    const API_URL = window.app?.API_URL || 'http://localhost:5000/api';

    // DOM Elements
    const loading = document.getElementById('loading');
    const emptyState = document.getElementById('empty-state');
    const productsList = document.getElementById('products-list');
    const productsTable = document.getElementById('products-tbody');
    const resetBtn = document.getElementById('reset-statuses-btn');



    // Reset status functionality
    resetBtn.addEventListener('click', async () => {
        if (confirm('Are you sure you want to reset all product statuses? This will clear workspace assignments.')) {
            try {
                showToast('Resetting product statuses...', 'info');
                const response = await fetch(`${API_URL}/workspace/reset-statuses`, {
                    method: 'POST'
                });
                const result = await response.json();

                if (result.success) {
                    showToast(`Reset ${result.data.reset_count} products to imported status`, 'success');
                    setTimeout(() => location.reload(), 1000);
                } else {
                    throw new Error(result.error || 'Failed to reset statuses');
                }
            } catch (error) {
                console.error('Reset error:', error);
                showToast('Failed to reset statuses: ' + error.message, 'error');
            }
        }
    });

    /**
     * Load workspace products
     */
    async function loadWorkspaceProducts() {
        try {
            const response = await fetch(`${API_URL}/workspace/products`);
            const result = await response.json();

            if (loading) loading.style.display = 'none';

            if (result.success && result.data.products && result.data.products.length > 0) {
                await displayProducts(result.data.products);
                if (productsList) productsList.style.display = 'block';
            } else {
                if (emptyState) emptyState.style.display = 'block';
            }
        } catch (error) {
            console.error('Error loading workspace products:', error);
            if (loading) loading.style.display = 'none';
            if (emptyState) emptyState.style.display = 'block';
            showToast('Failed to load workspace products', 'error');
        }
    }

    /**
     * Display products in table format
     */
    async function displayProducts(products) {
        const rows = [];

        for (const product of products) {
            // Use existing images from the product
            const images = product.images || [];

            // Don't make AI calls during initial load for performance
            const description = product.original_content?.description || product.title || '';
            const price = product.original_price || product.final_price || product.price || 'N/A';
            const title = product.title || 'Untitled Product';

            const imagesHtml = images.length > 0
                ? images.slice(0, 6).map(img => {
                    let imageUrl;

                    // Handle Cloudinary URLs only - no local fallback
                    if (img.cloudinary_url) {
                        imageUrl = img.cloudinary_url;
                    }
                    else if (img.url && img.url.startsWith('http')) {
                        // If it's a full HTTP URL (Cloudinary), use as-is
                        imageUrl = img.url;
                    }
                    else {
                        // No valid Cloudinary URL - skip this image
                        return '';
                    }

                    return `<img src="${imageUrl}" alt="Product" class="product-image"
                                 onerror="this.style.display='none'"
                                 onclick="previewImage('${imageUrl}', '${title}')">`;
                  }).join('')
                : '<span style="color: #9ca3af;">No images extracted</span>';

            rows.push(`
                <tr data-product-id="${product._id}">
                    <td>
                        <div class="product-images">
                            ${imagesHtml}
                        </div>
                        ${images.length > 6 ? `<span class="image-count">+${images.length - 6} more</span>` : ''}
                    </td>
                    <td class="product-title">
                        <div class="title-container">
                            <div class="title-text" id="title-${product._id}">${title}</div>
                            <button class="btn-ai" onclick="enhanceTitle('${product._id}')">✨ Enhance with Gemini</button>
                        </div>
                    </td>
                    <td class="product-description">
                        <div class="description-container">
                            <div class="description-text" id="description-${product._id}">${description}</div>
                            <button class="btn-ai" onclick="enhanceDescription('${product._id}')">✨ Enhance with Gemini</button>
                        </div>
                    </td>
                    <td class="product-price">
                        <div class="price-container">
                            <div class="price-display">$<span id="price-${product._id}">${price}</span></div>
                            <div class="price-controls">
                                <label>Markup %:</label>
                                <input type="number" id="markup-${product._id}" value="50" min="0" max="500"
                                       onchange="updatePrice('${product._id}', ${price})">
                                <button class="btn-small" onclick="applyMarkup('${product._id}', ${price})">Apply</button>
                            </div>
                        </div>
                    </td>
                    <td class="product-actions">
                        <button class="btn-preview" onclick="previewProduct('${product._id}')">👁️ Preview</button>
                        <button class="btn-select-images" onclick="selectImagesForShopify('${product._id}')">🖼️ Select Images</button>
                        <button class="btn-export" onclick="exportToShopify('${product._id}')">🚀 Export to Shopify</button>
                    </td>
                </tr>
            `);
        }

        console.log('Generated rows:', rows.length);
        if (productsTable) {
            productsTable.innerHTML = rows.join('');
            console.log('Table populated with', rows.length, 'rows');
        } else {
            console.error('productsTable element not found!');
        }
    }

    // Use global showToast function from app.js
    const showToast = window.app?.showToast || function(message, type) {
        console.log(`${type.toUpperCase()}: ${message}`);
    };

    // Load workspace products and saved state on page load
    loadWorkspaceProducts();
    loadSavedWorkspaceState();

    // Global functions for workspace features
    window.previewImage = function(imageUrl, title) {
        const modal = document.createElement('div');
        modal.className = 'image-modal';
        modal.innerHTML = `
            <div class="modal-content">
                <span class="close" onclick="this.parentElement.parentElement.remove()">&times;</span>
                <img src="${imageUrl}" alt="${title}" style="max-width: 90%; max-height: 90%;">
                <p>${title}</p>
            </div>
        `;
        document.body.appendChild(modal);
    };

    window.enhanceTitle = async function(productId) {
        const titleElement = document.getElementById(`title-${productId}`);
        const originalTitle = titleElement.textContent;

        // Show custom prompt modal
        const modal = document.createElement('div');
        modal.className = 'prompt-modal';
        modal.innerHTML = `
            <div class="modal-content">
                <span class="close" onclick="this.parentElement.parentElement.remove()">&times;</span>
                <h2>✨ Enhance Title with Gemini</h2>
                <p><strong>Original Title:</strong> ${originalTitle}</p>

                <div class="prompt-section">
                    <label for="title-prompt">Custom Enhancement Prompt:</label>
                    <textarea id="title-prompt" rows="4" placeholder="Enter your custom prompt for how you want the title enhanced...">AUNTY DIVA style - Create a SHORT product title (under 60 characters) in ALL CAPS format like: AUNTY DIVA CHUNKY HOOPS EARRINGS</textarea>
                </div>

                <div class="modal-actions">
                    <button class="btn-secondary" onclick="this.parentElement.parentElement.parentElement.remove()">Cancel</button>
                    <button class="btn-primary" onclick="processEnhancement('${productId}', 'title', '${originalTitle}')">✨ Enhance Title</button>
                </div>
            </div>
        `;
        document.body.appendChild(modal);
    };

    window.enhanceDescription = async function(productId) {
        const descElement = document.getElementById(`description-${productId}`);
        const originalDesc = descElement.textContent;

        // Show custom prompt modal
        const modal = document.createElement('div');
        modal.className = 'prompt-modal';
        modal.innerHTML = `
            <div class="modal-content">
                <span class="close" onclick="this.parentElement.parentElement.remove()">&times;</span>
                <h2>✨ Enhance Description with Gemini</h2>
                <p><strong>Original Description:</strong></p>
                <div class="original-content">${originalDesc}</div>

                <div class="prompt-section">
                    <label for="description-prompt">Custom Enhancement Prompt:</label>
                    <textarea id="description-prompt" rows="4" placeholder="Enter your custom prompt for how you want the description enhanced...">Premium product description with specifications, materials, and benefits - Create a detailed AUNTY DIVA style description with luxury language and specifications</textarea>
                </div>

                <div class="modal-actions">
                    <button class="btn-secondary" onclick="this.parentElement.parentElement.parentElement.remove()">Cancel</button>
                    <button class="btn-primary" onclick="processEnhancement('${productId}', 'description', \`${originalDesc.replace(/`/g, '\\`')}\`)">✨ Enhance Description</button>
                </div>
            </div>
        `;
        document.body.appendChild(modal);
    };

    window.processEnhancement = async function(productId, contentType, originalContent) {
        const modal = document.querySelector('.prompt-modal');
        const promptTextarea = document.getElementById(`${contentType}-prompt`);
        const customPrompt = promptTextarea.value.trim();

        if (!customPrompt) {
            showToast('Please enter a custom prompt', 'warning');
            return;
        }

        // Close modal
        modal.remove();

        // Update UI to show processing
        const element = document.getElementById(`${contentType}-${productId}`);
        const originalText = element.textContent || element.innerHTML;
        element.innerHTML = '✨ Enhancing...';

        try {
            const response = await fetch(`${API_URL}/ai/enhance-content`, {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({
                    content: originalContent,
                    type: contentType,
                    provider: 'gemini',
                    style: customPrompt
                })
            });

            const result = await response.json();
            console.log(`${contentType} enhancement result:`, result);

            if (result.success && result.data && result.data.enhanced_content) {
                // Update UI
                if (contentType === 'description') {
                    element.innerHTML = result.data.enhanced_content.replace(/\n/g, '<br>');
                } else {
                    element.textContent = result.data.enhanced_content;
                }

                // Save the enhanced content to the database
                await saveEnhancedContent(productId, contentType, result.data.enhanced_content);

                showToast(`${contentType.charAt(0).toUpperCase() + contentType.slice(1)} enhanced and saved successfully!`, 'success');
            } else {
                console.error('Enhancement failed:', result);
                throw new Error(result.error || 'Enhancement failed');
            }
        } catch (error) {
            // Restore original content on error
            if (contentType === 'description') {
                element.innerHTML = originalText;
            } else {
                element.textContent = originalContent;
            }
            showToast(`Failed to enhance ${contentType}: ${error.message}`, 'error');
        }
    };

    window.updatePrice = function(productId, originalPrice) {
        const markupInput = document.getElementById(`markup-${productId}`);
        const priceDisplay = document.getElementById(`price-${productId}`);
        const markup = parseFloat(markupInput.value) || 0;
        const newPrice = (parseFloat(originalPrice) * (1 + markup / 100)).toFixed(2);
        priceDisplay.textContent = newPrice;

        // Save price change to state
        if (!window.priceChanges) window.priceChanges = {};
        window.priceChanges[productId] = {
            originalPrice: parseFloat(originalPrice),
            markup: markup,
            finalPrice: parseFloat(newPrice)
        };
    };

    window.applyMarkup = function(productId, originalPrice) {
        updatePrice(productId, originalPrice);
        showToast('Markup applied successfully!', 'success');
        saveWorkspaceState(); // Auto-save when markup is applied
    };

    window.previewProduct = async function(productId) {
        try {
            // Get product data from API
            const response = await fetch(`${API_URL}/products/${productId}`);
            const result = await response.json();

            if (!result.success) {
                throw new Error(result.error || 'Failed to get product data');
            }

            const product = result.data;
            const title = product.title || 'Untitled Product';
            const description = product.description || 'No description available';
            const price = product.final_price || product.original_price || 0;

            // Get selected images or all images if none selected
            const imageSelection = window.imageSelections && window.imageSelections[productId];
            let imagesToShow = product.images || [];

            if (imageSelection && imageSelection.selectedImages.length > 0) {
                // Show only selected images
                imagesToShow = imageSelection.selectedImages.map(index => product.images[index]).filter(img => img);

                // Reorder so main image is first
                if (imageSelection.mainImageIndex >= 0 && imageSelection.mainImageIndex < imagesToShow.length) {
                    const mainImage = imagesToShow[imageSelection.mainImageIndex];
                    imagesToShow.splice(imageSelection.mainImageIndex, 1);
                    imagesToShow.unshift(mainImage);
                }
            }

            const imageUrls = imagesToShow.map(img => img.cloudinary_url || img.url).filter(url => url);

            const modal = document.createElement('div');
            modal.className = 'preview-modal';
            modal.innerHTML = `
                <div class="modal-content large">
                    <span class="close" onclick="this.parentElement.parentElement.remove()">&times;</span>
                    <h2>Shopify Export Preview</h2>
                    ${imageSelection ? '<p class="preview-note">Showing selected images for Shopify export</p>' : '<p class="preview-note">No images selected - showing all available images</p>'}
                    <div class="preview-content">
                        <div class="preview-images">
                            ${imageUrls.length > 0
                                ? imageUrls.map((url, index) => `
                                    <div class="preview-image-container">
                                        <img src="${url}" alt="Product Image ${index + 1}">
                                        ${index === 0 && imageSelection ? '<span class="main-image-badge">Main Image</span>' : ''}
                                    </div>
                                `).join('')
                                : '<p>No images available</p>'
                            }
                        </div>
                        <div class="preview-details">
                            <h3>${title}</h3>
                            <div class="preview-description">${description}</div>
                            <div class="preview-price">$${price}</div>
                            ${imageSelection
                                ? `<div class="preview-stats">
                                     <p><strong>Selected Images:</strong> ${imageSelection.selectedImages.length}</p>
                                     <p><strong>Main Image:</strong> Image ${imageSelection.selectedImages[imageSelection.mainImageIndex] + 1}</p>
                                   </div>`
                                : '<div class="preview-warning">⚠️ Please select images before exporting to Shopify</div>'
                            }
                        </div>
                    </div>
                    <div class="modal-actions">
                        ${!imageSelection
                            ? '<button class="btn-secondary" onclick="selectImagesForShopify(\'' + productId + '\'); this.parentElement.parentElement.parentElement.remove()">Select Images First</button>'
                            : ''
                        }
                        <button class="btn-export" onclick="exportToShopify('${productId}')">🚀 Export to Shopify</button>
                    </div>
                </div>
            `;
            document.body.appendChild(modal);

        } catch (error) {
            console.error('Error previewing product:', error);
            showToast('Failed to load product preview: ' + error.message, 'error');
        }
    };

    window.selectImagesForShopify = async function(productId) {
        try {
            // Get product data
            const response = await fetch(`${API_URL}/products/${productId}`);
            const result = await response.json();

            if (!result.success) {
                throw new Error(result.error || 'Failed to get product data');
            }

            const product = result.data;
            const images = product.images || [];

            if (images.length === 0) {
                showToast('No images available for this product', 'warning');
                return;
            }

            // Create image selection modal
            const modal = document.createElement('div');
            modal.className = 'image-selection-modal';
            modal.innerHTML = `
                <div class="modal-content large">
                    <span class="close" onclick="this.parentElement.parentElement.remove()">&times;</span>
                    <h2>Select Images for Shopify Export</h2>
                    <p>Choose which images to export and select the main image:</p>

                    <div class="image-selection-grid">
                        ${images.map((img, index) => `
                            <div class="image-selection-item" data-index="${index}">
                                <div class="image-container">
                                    <img src="${img.cloudinary_url || img.url}" alt="Product Image ${index + 1}"
                                         onerror="this.style.display='none'">
                                    <div class="image-overlay">
                                        <label class="checkbox-container">
                                            <input type="checkbox" class="image-checkbox" data-index="${index}" ${index === 0 ? 'checked' : ''}>
                                            <span class="checkmark"></span>
                                        </label>
                                        <label class="radio-container">
                                            <input type="radio" name="main-image-${productId}" class="main-image-radio" data-index="${index}" ${index === 0 ? 'checked' : ''}>
                                            <span class="radio-label">Main</span>
                                        </label>
                                        <button class="btn-remove-bg-single" onclick="removeBackgroundSingle('${productId}', ${index})" title="Remove background from this image">
                                            ✂️
                                        </button>
                                        <button class="btn-undo-bg" onclick="undoBackgroundRemoval('${productId}', ${index})" title="Undo background removal" style="display: none;">
                                            ↶
                                        </button>
                                    </div>
                                </div>
                                <div class="image-info">
                                    Image ${index + 1}
                                </div>
                            </div>
                        `).join('')}
                    </div>

                    <div class="modal-actions">
                        <button class="btn-secondary" onclick="this.parentElement.parentElement.parentElement.remove()">Cancel</button>
                        <button class="btn-primary" onclick="confirmImageSelection('${productId}')">Confirm Selection</button>
                    </div>
                </div>
            `;

            document.body.appendChild(modal);

        } catch (error) {
            console.error('Error selecting images:', error);
            showToast('Failed to load images: ' + error.message, 'error');
        }
    };

    window.confirmImageSelection = function(productId) {
        try {
            const modal = document.querySelector('.image-selection-modal');
            const selectedImages = [];
            let mainImageIndex = 0;

            // Get selected images
            const checkboxes = modal.querySelectorAll('.image-checkbox:checked');
            checkboxes.forEach(checkbox => {
                selectedImages.push(parseInt(checkbox.dataset.index));
            });

            // Get main image
            const mainRadio = modal.querySelector('.main-image-radio:checked');
            if (mainRadio) {
                mainImageIndex = parseInt(mainRadio.dataset.index);
            }

            if (selectedImages.length === 0) {
                showToast('Please select at least one image', 'warning');
                return;
            }

            if (!selectedImages.includes(mainImageIndex)) {
                showToast('Main image must be one of the selected images', 'warning');
                return;
            }

            // Store selection in product data for export
            window.imageSelections = window.imageSelections || {};
            window.imageSelections[productId] = {
                selectedImages: selectedImages,
                mainImageIndex: selectedImages.indexOf(mainImageIndex) // Index within selected images
            };

            modal.remove();
            showToast(`Selected ${selectedImages.length} images for export`, 'success');

            // Update visual indicator and save state
            updateProductSelectionIndicator(productId, window.imageSelections[productId]);
            saveWorkspaceState();

        } catch (error) {
            console.error('Error confirming selection:', error);
            showToast('Failed to confirm selection: ' + error.message, 'error');
        }
    };

    window.exportToShopify = async function(productId) {
        try {
            // Check if images are selected
            const imageSelection = window.imageSelections && window.imageSelections[productId];

            if (!imageSelection) {
                showToast('Please select images first using the "Select Images" button', 'warning');
                return;
            }

            showToast('Exporting to Shopify...', 'info');

            const response = await fetch(`${API_URL}/shopify/export-product/${productId}`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    selected_images: imageSelection.selectedImages,
                    main_image_index: imageSelection.mainImageIndex
                })
            });

            const result = await response.json();

            if (result.success) {
                showToast('Product exported to Shopify successfully!', 'success');
                // Refresh the workspace to show updated status
                setTimeout(() => location.reload(), 1000);
            } else {
                throw new Error(result.error || 'Failed to export to Shopify');
            }

        } catch (error) {
            console.error('Export error:', error);
            showToast('Failed to export to Shopify: ' + error.message, 'error');
        }
    };



    window.removeBackgroundSingle = async function(productId, imageIndex) {
        try {
            if (confirm('Remove background from this image using Cloudinary AI?')) {
                showToast('Processing image...', 'info');

                // Store original image for undo
                const modal = document.querySelector('.image-selection-modal');
                const imageElement = modal.querySelector(`[data-index="${imageIndex}"] img`);
                const originalSrc = imageElement.src;

                // Store undo data
                if (!window.backgroundRemovalHistory) window.backgroundRemovalHistory = {};
                if (!window.backgroundRemovalHistory[productId]) window.backgroundRemovalHistory[productId] = {};
                window.backgroundRemovalHistory[productId][imageIndex] = {
                    originalUrl: originalSrc,
                    hasBackgroundRemoved: false
                };

                const response = await fetch(`${API_URL}/images/remove-background/${productId}`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        provider: 'cloudinary',
                        image_indices: [imageIndex]
                    })
                });

                const result = await response.json();

                if (result.success && result.data && result.data.results && result.data.results.length > 0) {
                    const processedResult = result.data.results[0];
                    if (processedResult.success && processedResult.processed_cloudinary_url) {
                        // Replace the image in the modal
                        imageElement.src = processedResult.processed_cloudinary_url;

                        // Update undo data
                        window.backgroundRemovalHistory[productId][imageIndex] = {
                            originalUrl: originalSrc,
                            processedUrl: processedResult.processed_cloudinary_url,
                            hasBackgroundRemoved: true
                        };

                        // Show undo button and hide remove button
                        const removeBtn = modal.querySelector(`[data-index="${imageIndex}"] .btn-remove-bg-single`);
                        const undoBtn = modal.querySelector(`[data-index="${imageIndex}"] .btn-undo-bg`);
                        if (removeBtn) removeBtn.style.display = 'none';
                        if (undoBtn) undoBtn.style.display = 'inline-block';

                        // Update the main workspace table image as well
                        updateWorkspaceProductImage(productId, imageIndex, processedResult.processed_cloudinary_url);

                        showToast('Background removed successfully!', 'success');
                    } else {
                        throw new Error(processedResult.error || 'Failed to process image');
                    }
                } else {
                    throw new Error(result.error || 'Failed to remove background');
                }
            }

        } catch (error) {
            console.error('Single background removal error:', error);
            showToast('Failed to remove background: ' + error.message, 'error');
        }
    };

    window.undoBackgroundRemoval = function(productId, imageIndex) {
        try {
            const history = window.backgroundRemovalHistory?.[productId]?.[imageIndex];
            if (!history || !history.hasBackgroundRemoved) {
                showToast('No background removal to undo', 'warning');
                return;
            }

            // Restore original image
            const modal = document.querySelector('.image-selection-modal');
            const imageElement = modal.querySelector(`[data-index="${imageIndex}"] img`);
            imageElement.src = history.originalUrl;

            // Update history
            window.backgroundRemovalHistory[productId][imageIndex].hasBackgroundRemoved = false;

            // Show remove button and hide undo button
            const removeBtn = modal.querySelector(`[data-index="${imageIndex}"] .btn-remove-bg-single`);
            const undoBtn = modal.querySelector(`[data-index="${imageIndex}"] .btn-undo-bg`);
            if (removeBtn) removeBtn.style.display = 'inline-block';
            if (undoBtn) undoBtn.style.display = 'none';

            // Update the main workspace table image as well
            updateWorkspaceProductImage(productId, imageIndex, history.originalUrl);

            showToast('Background removal undone', 'success');

        } catch (error) {
            console.error('Undo background removal error:', error);
            showToast('Failed to undo background removal: ' + error.message, 'error');
        }
    };

    function updateWorkspaceProductImage(productId, imageIndex, newImageUrl) {
        try {
            // Find the product row in the workspace table
            const productRow = document.querySelector(`tr[data-product-id="${productId}"]`);
            if (!productRow) return;

            // Find all product images in that row
            const productImages = productRow.querySelectorAll('.product-image');

            // Update the specific image if it exists
            if (productImages[imageIndex]) {
                productImages[imageIndex].src = newImageUrl;

                // Add a visual indicator that this image was processed
                productImages[imageIndex].style.border = '2px solid #10b981';
                productImages[imageIndex].title = 'Background removed';

                // Remove the border after 3 seconds
                setTimeout(() => {
                    if (productImages[imageIndex]) {
                        productImages[imageIndex].style.border = '';
                    }
                }, 3000);
            }

        } catch (error) {
            console.error('Error updating workspace product image:', error);
        }
    }

    async function saveEnhancedContent(productId, contentType, enhancedContent) {
        try {
            const response = await fetch(`${API_URL}/products/${productId}`, {
                method: 'PUT',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    [contentType]: enhancedContent,
                    [`ai_enhanced_${contentType}`]: true,
                    updated_at: new Date().toISOString()
                })
            });

            const result = await response.json();

            if (!result.success) {
                throw new Error(result.error || 'Failed to save enhanced content');
            }

            console.log(`Enhanced ${contentType} saved successfully`);

        } catch (error) {
            console.error(`Error saving enhanced ${contentType}:`, error);
            showToast(`Failed to save enhanced ${contentType}: ${error.message}`, 'error');
        }
    }

    // Workspace state management
    function saveWorkspaceState() {
        try {
            const workspaceState = {
                imageSelections: window.imageSelections || {},
                priceChanges: window.priceChanges || {},
                timestamp: new Date().toISOString()
            };

            localStorage.setItem('workspaceState', JSON.stringify(workspaceState));
            showToast('Workspace state saved', 'success');

        } catch (error) {
            console.error('Error saving workspace state:', error);
            showToast('Failed to save workspace state', 'error');
        }
    }

    function loadSavedWorkspaceState() {
        try {
            const savedState = localStorage.getItem('workspaceState');
            console.log('Loading saved state:', savedState);

            if (savedState) {
                const workspaceState = JSON.parse(savedState);
                console.log('Parsed workspace state:', workspaceState);

                // Restore image selections
                window.imageSelections = workspaceState.imageSelections || {};

                // Restore price changes
                window.priceChanges = workspaceState.priceChanges || {};

                // Wait for DOM to be ready before applying changes
                setTimeout(() => {
                    // Apply saved price changes to the UI
                    Object.keys(window.priceChanges).forEach(productId => {
                        const priceElement = document.getElementById(`price-${productId}`);
                        const markupElement = document.getElementById(`markup-${productId}`);

                        if (priceElement && window.priceChanges[productId]) {
                            priceElement.textContent = window.priceChanges[productId].finalPrice;
                            console.log(`Applied price for ${productId}:`, window.priceChanges[productId].finalPrice);
                        }

                        if (markupElement && window.priceChanges[productId]) {
                            markupElement.value = window.priceChanges[productId].markup;
                            console.log(`Applied markup for ${productId}:`, window.priceChanges[productId].markup);
                        }
                    });

                    // Show indicators for products with selections
                    Object.keys(window.imageSelections).forEach(productId => {
                        const selection = window.imageSelections[productId];
                        if (selection && selection.selectedImages.length > 0) {
                            updateProductSelectionIndicator(productId, selection);
                            console.log(`Applied selection indicator for ${productId}:`, selection);
                        }
                    });

                    if (Object.keys(window.imageSelections).length > 0 || Object.keys(window.priceChanges).length > 0) {
                        showToast('Workspace state restored successfully', 'success');
                    }
                }, 1000); // Wait 1 second for products to load

                console.log('Workspace state loaded:', workspaceState);
            } else {
                console.log('No saved workspace state found');
            }
        } catch (error) {
            console.error('Error loading workspace state:', error);
            showToast('Failed to load saved workspace state', 'error');
        }
    }

    function updateProductSelectionIndicator(productId, selection) {
        const row = document.querySelector(`tr[data-product-id="${productId}"]`);
        if (row) {
            // Add visual indicator that images are selected
            const selectButton = row.querySelector('.btn-select-images');
            if (selectButton) {
                selectButton.textContent = `🖼️ ${selection.selectedImages.length} Selected`;
                selectButton.style.background = 'var(--emerald-500)';
            }
        }
    }

    function clearWorkspaceState() {
        try {
            localStorage.removeItem('workspaceState');
            window.imageSelections = {};
            window.priceChanges = {};
            showToast('Workspace state cleared', 'info');
            location.reload();
        } catch (error) {
            console.error('Error clearing workspace state:', error);
            showToast('Failed to clear workspace state', 'error');
        }
    }

    // Auto-save when changes are made
    window.addEventListener('beforeunload', function() {
        if (Object.keys(window.imageSelections || {}).length > 0 ||
            Object.keys(window.priceChanges || {}).length > 0) {
            saveWorkspaceState();
        }
    });

    // Global save function
    window.saveWorkspace = saveWorkspaceState;
    window.clearWorkspace = clearWorkspaceState;
});
