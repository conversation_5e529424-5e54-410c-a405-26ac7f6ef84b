<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Shopify Workspace - R.A.V.E</title>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Geist:wght@100;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        :root {
            --primary-50: #eff6ff;
            --primary-100: #dbeafe;
            --primary-500: #3b82f6;
            --primary-600: #2563eb;
            --emerald-500: #10b981;
            --emerald-600: #059669;
            --violet-500: #8b5cf6;
            --rose-500: #f43f5e;
            --amber-500: #f59e0b;
            --gray-50: #f9fafb;
            --gray-100: #f3f4f6;
            --gray-200: #e5e7eb;
            --gray-300: #d1d5db;
            --gray-400: #9ca3af;
            --gray-500: #6b7280;
            --gray-600: #4b5563;
            --gray-700: #374151;
            --gray-800: #1f2937;
            --gray-900: #111827;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Geist', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            font-weight: 400;
            background: #ffffff;
            min-height: 100vh;
            color: var(--gray-800);
            line-height: 1.6;
        }

        .header {
            background: white;
            border-bottom: 1px solid var(--gray-200);
            padding: 1rem 2rem;
            position: sticky;
            top: 0;
            z-index: 100;
            box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
        }

        .header-content {
            max-width: 1400px;
            margin: 0 auto;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .logo {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            font-size: 1.5rem;
            font-weight: 700;
            color: var(--gray-800);
        }

        .nav-buttons {
            display: flex;
            gap: 1rem;
        }

        .nav-btn {
            padding: 0.5rem 1rem;
            border-radius: 6px;
            text-decoration: none;
            font-weight: 500;
            transition: all 0.2s ease;
        }

        .nav-btn.primary {
            background: var(--primary-500);
            color: white;
        }

        .nav-btn.secondary {
            background: var(--gray-100);
            color: var(--gray-600);
        }

        .nav-btn:hover {
            transform: translateY(-1px);
        }

        .nav-btn.danger {
            background: #dc2626;
            color: white;
        }

        .nav-btn.danger:hover {
            background: #b91c1c;
        }

        .main-container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 2rem;
            min-height: calc(100vh - 120px);
        }

        .workspace-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 2rem;
            padding: 1.5rem;
            background: var(--gray-50);
            border-radius: 12px;
            border: 1px solid var(--gray-200);
        }

        .workspace-title-section {
            flex: 1;
        }

        .workspace-title {
            font-size: 2rem;
            font-weight: 700;
            color: var(--gray-800);
            margin-bottom: 0.5rem;
        }

        .workspace-subtitle {
            color: var(--gray-600);
            font-size: 1.1rem;
        }

        .workspace-actions {
            display: flex;
            gap: 1rem;
            align-items: center;
        }

        .btn-save {
            background: var(--emerald-500);
            color: white;
            border: none;
            padding: 0.75rem 1.5rem;
            border-radius: 8px;
            font-size: 0.875rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .btn-save:hover {
            background: var(--emerald-600);
            transform: translateY(-1px);
        }

        .btn-clear-state {
            background: var(--gray-500);
            color: white;
            border: none;
            padding: 0.75rem 1.5rem;
            border-radius: 8px;
            font-size: 0.875rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .btn-clear-state:hover {
            background: var(--gray-600);
            transform: translateY(-1px);
        }

        .products-table {
            width: 100%;
            border-collapse: collapse;
            background: white;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
        }

        .products-table th {
            background: #f8fafc;
            padding: 1rem;
            text-align: left;
            font-weight: 600;
            color: #374151;
            border-bottom: 1px solid #e5e7eb;
        }

        .products-table td {
            padding: 1rem;
            border-bottom: 1px solid #f3f4f6;
            vertical-align: top;
        }

        .products-table tr:last-child td {
            border-bottom: none;
        }

        .product-images {
            display: flex;
            gap: 0.5rem;
            flex-wrap: wrap;
            max-width: 200px;
        }

        .product-image {
            width: 60px;
            height: 60px;
            object-fit: cover;
            border-radius: 4px;
            border: 1px solid #e5e7eb;
            cursor: pointer;
            transition: transform 0.2s;
            margin-right: 8px;
        }

        .product-image:hover {
            transform: scale(1.1);
        }

        .image-count {
            font-size: 0.8rem;
            color: var(--gray-500);
            margin-top: 4px;
            display: block;
        }

        .product-title {
            font-weight: 500;
            color: #111827;
            max-width: 300px;
        }

        .product-description {
            max-width: 400px;
            color: #6b7280;
            line-height: 1.5;
        }

        .product-price {
            font-weight: 600;
            color: #059669;
            font-size: 1.1rem;
        }

        .title-container, .description-container {
            display: flex;
            flex-direction: column;
            gap: 8px;
        }

        .btn-ai {
            background: linear-gradient(135deg, #8b5cf6, #3b82f6);
            color: white;
            border: none;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 0.8rem;
            cursor: pointer;
            transition: all 0.2s;
            align-self: flex-start;
        }

        .btn-ai:hover {
            transform: translateY(-1px);
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        .price-container {
            display: flex;
            flex-direction: column;
            gap: 8px;
        }

        .price-display {
            font-size: 1.2rem;
            font-weight: 600;
            color: var(--emerald-600);
        }

        .price-controls {
            display: flex;
            flex-direction: column;
            gap: 4px;
        }

        .price-controls label {
            font-size: 0.8rem;
            color: var(--gray-600);
        }

        .price-controls input {
            width: 80px;
            padding: 4px;
            border: 1px solid var(--gray-300);
            border-radius: 4px;
            font-size: 0.9rem;
        }

        .btn-small {
            background: var(--primary-500);
            color: white;
            border: none;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 0.8rem;
            cursor: pointer;
            transition: background 0.2s;
        }

        .btn-small:hover {
            background: var(--primary-600);
        }

        .product-actions {
            display: flex;
            flex-direction: column;
            gap: 8px;
        }

        .btn-preview, .btn-export, .btn-select-images, .btn-bg-removal {
            padding: 8px 12px;
            border: none;
            border-radius: 6px;
            font-size: 0.9rem;
            cursor: pointer;
            transition: all 0.3s ease;
            text-align: center;
            font-weight: 500;
            display: inline-flex;
            align-items: center;
            gap: 0.25rem;
            text-decoration: none;
        }

        .btn-preview {
            background: var(--primary-500);
            color: white;
        }

        .btn-preview:hover {
            background: var(--primary-600);
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
        }

        .btn-export {
            background: var(--emerald-500);
            color: white;
        }

        .btn-export:hover {
            background: var(--emerald-600);
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(16, 185, 129, 0.3);
        }

        .btn-select-images {
            background: var(--violet-500);
            color: white;
        }

        .btn-select-images:hover {
            background: var(--violet-600);
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(139, 92, 246, 0.3);
        }

        .btn-bg-removal {
            background: var(--amber-500);
            color: white;
        }

        .btn-bg-removal:hover {
            background: #d97706;
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(245, 158, 11, 0.3);
        }

        /* Modal Styles */
        .image-modal, .preview-modal {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.8);
            display: flex;
            justify-content: center;
            align-items: center;
            z-index: 1000;
        }

        .modal-content {
            background: white;
            padding: 2rem;
            border-radius: 12px;
            max-width: 90%;
            max-height: 90%;
            position: relative;
            text-align: center;
            overflow: hidden;
        }

        .modal-content img {
            max-width: 100%;
            max-height: 70vh;
            object-fit: contain;
            border-radius: 8px;
        }

        .modal-content.large {
            max-width: 80%;
            max-height: 80%;
            overflow-y: auto;
        }

        .close {
            position: absolute;
            top: 10px;
            right: 15px;
            font-size: 28px;
            font-weight: bold;
            cursor: pointer;
            color: var(--gray-500);
        }

        .close:hover {
            color: var(--gray-800);
        }

        .preview-content {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 2rem;
            margin-top: 1rem;
        }

        .preview-images {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(100px, 1fr));
            gap: 1rem;
        }

        .preview-images img {
            width: 100%;
            height: 100px;
            object-fit: cover;
            border-radius: 8px;
            border: 1px solid var(--gray-200);
        }

        .preview-details h3 {
            color: var(--gray-800);
            margin-bottom: 1rem;
            text-align: left;
        }

        .preview-description {
            color: var(--gray-600);
            line-height: 1.6;
            text-align: left;
            margin-bottom: 1rem;
        }

        .preview-price {
            font-size: 1.5rem;
            font-weight: 600;
            color: var(--emerald-600);
            text-align: left;
        }

        .modal-actions {
            margin-top: 2rem;
            text-align: center;
        }

        .product-card {
            background: white;
            border-radius: 12px;
            padding: 1.5rem;
            box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
            border: 1px solid var(--gray-200);
            transition: all 0.2s ease;
        }

        .product-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px 0 rgba(0, 0, 0, 0.15);
        }

        .product-header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: 1rem;
        }

        .product-title {
            font-size: 1.1rem;
            font-weight: 600;
            color: var(--gray-800);
            line-height: 1.4;
            flex: 1;
            margin-right: 1rem;
        }

        .product-price {
            background: var(--emerald-50);
            color: var(--emerald-700);
            padding: 0.25rem 0.75rem;
            border-radius: 20px;
            font-weight: 600;
            font-size: 0.9rem;
        }

        .product-images {
            margin: 1rem 0;
        }

        .main-image {
            width: 100%;
            height: 200px;
            object-fit: cover;
            border-radius: 8px;
            margin-bottom: 0.5rem;
        }

        .gallery-images {
            display: flex;
            gap: 0.5rem;
            overflow-x: auto;
            padding: 0.5rem 0;
        }

        .gallery-image {
            width: 60px;
            height: 60px;
            object-fit: cover;
            border-radius: 6px;
            cursor: pointer;
            transition: transform 0.2s ease;
            flex-shrink: 0;
        }

        .gallery-image:hover {
            transform: scale(1.1);
        }

        .product-meta {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-top: 1rem;
            padding-top: 1rem;
            border-top: 1px solid var(--gray-200);
            font-size: 0.875rem;
            color: var(--gray-600);
        }

        .order-info {
            display: flex;
            flex-direction: column;
            gap: 0.25rem;
        }

        .image-count {
            background: var(--primary-50);
            color: var(--primary-700);
            padding: 0.25rem 0.5rem;
            border-radius: 12px;
            font-weight: 500;
        }

        .loading {
            text-align: center;
            padding: 3rem;
            color: var(--gray-600);
            font-size: 1.1rem;
        }

        .empty-state {
            text-align: center;
            padding: 4rem 2rem;
            color: var(--gray-600);
        }

        .empty-state h3 {
            font-size: 1.5rem;
            margin-bottom: 1rem;
            color: var(--gray-700);
        }

        .empty-state p {
            font-size: 1.1rem;
            margin-bottom: 2rem;
        }

        .empty-state .btn {
            background: var(--primary-500);
            color: white;
            padding: 0.75rem 1.5rem;
            border: none;
            border-radius: 8px;
            font-weight: 600;
            text-decoration: none;
            display: inline-block;
            transition: all 0.2s ease;
        }

        .empty-state .btn:hover {
            background: var(--primary-600);
            transform: translateY(-1px);
        }

        /* Image Selection Modal */
        .image-selection-modal {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.8);
            display: flex;
            justify-content: center;
            align-items: center;
            z-index: 1000;
        }

        .image-selection-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(180px, 1fr));
            gap: 1.25rem;
            margin: 1.5rem 0;
            max-height: 500px;
            overflow-y: auto;
            padding: 1.5rem;
            border: 1px solid var(--gray-200);
            border-radius: 12px;
            background: linear-gradient(135deg, var(--gray-50) 0%, var(--gray-100) 100%);
            box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.05);
        }

        .image-selection-item {
            position: relative;
            border: 2px solid transparent;
            border-radius: 12px;
            overflow: hidden;
            transition: all 0.3s ease;
            background: white;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }

        .image-selection-item:hover {
            border-color: var(--primary-500);
            transform: translateY(-4px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
        }

        .image-container {
            position: relative;
            width: 100%;
            height: 160px;
            overflow: hidden;
            border-radius: 8px 8px 0 0;
        }

        .image-container img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }

        .image-overlay {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0, 0, 0, 0.5);
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            padding: 0.5rem;
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        .image-selection-item:hover .image-overlay {
            opacity: 1;
        }

        .checkbox-container, .radio-container {
            display: flex;
            align-items: center;
            gap: 0.25rem;
            color: white;
            font-size: 0.875rem;
            font-weight: 500;
        }

        .image-checkbox, .main-image-radio {
            width: 18px;
            height: 18px;
        }

        .radio-label {
            background: var(--emerald-500);
            padding: 0.25rem 0.5rem;
            border-radius: 4px;
            font-size: 0.75rem;
        }

        .image-info {
            padding: 0.75rem;
            text-align: center;
            font-size: 0.875rem;
            color: var(--gray-600);
            background: white;
            font-weight: 500;
            border-top: 1px solid var(--gray-100);
        }

        .btn-select-images {
            background: var(--violet-500);
            color: white;
            border: none;
            padding: 0.5rem 1rem;
            border-radius: 6px;
            font-size: 0.875rem;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s ease;
            margin-right: 0.5rem;
        }

        .btn-select-images:hover {
            background: var(--violet-600);
            transform: translateY(-1px);
        }

        .btn-bg-removal {
            background: var(--amber-500);
            color: white;
            border: none;
            padding: 0.5rem 1rem;
            border-radius: 6px;
            font-size: 0.875rem;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s ease;
            margin-right: 0.5rem;
        }

        .btn-bg-removal:hover {
            background: #d97706;
            transform: translateY(-1px);
        }

        .btn-remove-bg-single, .btn-undo-bg {
            background: var(--amber-500);
            color: white;
            border: none;
            padding: 0.25rem 0.5rem;
            border-radius: 4px;
            font-size: 0.75rem;
            cursor: pointer;
            transition: all 0.3s ease;
            margin-left: 0.25rem;
        }

        .btn-remove-bg-single:hover, .btn-undo-bg:hover {
            background: #d97706;
            transform: scale(1.05);
        }

        .btn-undo-bg {
            background: var(--gray-500);
        }

        .btn-undo-bg:hover {
            background: var(--gray-600);
        }

        .bg-removal-modal {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.8);
            display: flex;
            justify-content: center;
            align-items: center;
            z-index: 1000;
        }

        .provider-selection {
            margin: 1rem 0;
            padding: 1rem;
            background: var(--gray-50);
            border-radius: 8px;
        }

        .provider-selection label {
            display: block;
            margin-bottom: 0.5rem;
            font-weight: 500;
            color: var(--gray-700);
        }

        .provider-selection select {
            width: 100%;
            padding: 0.5rem;
            border: 1px solid var(--gray-300);
            border-radius: 6px;
            font-size: 0.875rem;
        }

        .preview-note {
            background: var(--primary-50);
            color: var(--primary-600);
            padding: 0.75rem;
            border-radius: 6px;
            margin-bottom: 1rem;
            font-size: 0.875rem;
            border-left: 4px solid var(--primary-500);
        }

        .preview-image-container {
            position: relative;
            display: inline-block;
            margin-right: 1rem;
            margin-bottom: 1rem;
        }

        .main-image-badge {
            position: absolute;
            top: 0.5rem;
            left: 0.5rem;
            background: var(--emerald-500);
            color: white;
            padding: 0.25rem 0.5rem;
            border-radius: 4px;
            font-size: 0.75rem;
            font-weight: 500;
        }

        .preview-stats {
            background: var(--gray-50);
            padding: 1rem;
            border-radius: 6px;
            margin-top: 1rem;
        }

        .preview-stats p {
            margin: 0.25rem 0;
            font-size: 0.875rem;
        }

        .preview-warning {
            background: var(--amber-50);
            color: var(--amber-700);
            padding: 0.75rem;
            border-radius: 6px;
            margin-top: 1rem;
            font-size: 0.875rem;
            border-left: 4px solid var(--amber-500);
        }

        /* Toast Notifications */
        .toast {
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 10000;
            min-width: 300px;
            max-width: 500px;
            border-radius: 8px;
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
            transform: translateX(100%);
            transition: all 0.3s ease;
            opacity: 0;
        }

        .toast-show {
            transform: translateX(0);
            opacity: 1;
        }

        .toast-content {
            display: flex;
            align-items: center;
            padding: 1rem;
            gap: 0.75rem;
        }

        .toast-icon {
            font-size: 1.25rem;
            flex-shrink: 0;
        }

        .toast-message {
            flex: 1;
            font-size: 0.875rem;
            font-weight: 500;
        }

        .toast-close {
            background: none;
            border: none;
            font-size: 1.25rem;
            cursor: pointer;
            opacity: 0.7;
            transition: opacity 0.2s;
            flex-shrink: 0;
        }

        .toast-close:hover {
            opacity: 1;
        }

        .toast-success {
            background: var(--emerald-50);
            color: var(--emerald-800);
            border-left: 4px solid var(--emerald-500);
        }

        .toast-error {
            background: var(--red-50);
            color: var(--red-800);
            border-left: 4px solid var(--red-500);
        }

        .toast-warning {
            background: var(--amber-50);
            color: var(--amber-800);
            border-left: 4px solid var(--amber-500);
        }

        .toast-info {
            background: var(--blue-50);
            color: var(--blue-800);
            border-left: 4px solid var(--blue-500);
        }

        @media (max-width: 768px) {
            .main-container {
                padding: 1rem;
            }

            .products-grid {
                grid-template-columns: 1fr;
            }

            .nav-buttons {
                flex-direction: column;
                gap: 0.5rem;
            }

            .image-selection-grid {
                grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
                gap: 0.75rem;
            }

            .image-container {
                height: 120px;
            }
        }
    </style>
</head>
<body>
    <header class="header">
        <div class="header-content">
            <div class="logo">
                R.A.V.E
            </div>
            <nav class="nav-buttons">
                <a href="index.html" class="nav-btn secondary">Dashboard</a>
                <a href="upload.html" class="nav-btn secondary">Upload Data</a>
                <a href="workspace.html" class="nav-btn primary">Shopify Workspace</a>
                <button class="nav-btn danger" id="reset-statuses-btn">Reset Status</button>
            </nav>
        </div>
    </header>

    <main class="main-container">
        <div class="workspace-header">
            <div class="workspace-title-section">
                <h1 class="workspace-title">Shopify Workspace</h1>
                <p class="workspace-subtitle">Selected products ready for Shopify export</p>
            </div>
            <div class="workspace-actions">
                <button class="btn-save" onclick="saveWorkspace()">💾 Save Changes</button>
                <button class="btn-clear-state" onclick="clearWorkspace()">🗑️ Clear Saved State</button>
            </div>
        </div>

        <div class="loading" id="loading">Loading workspace products...</div>

        <div class="empty-state" id="empty-state" style="display: none;">
            <h3>🛒 No Products in Workspace</h3>
            <p>You haven't added any products to your Shopify workspace yet.</p>
            <p>To add products:</p>
            <ol style="text-align: left; max-width: 400px; margin: 1rem auto;">
                <li>Go to the Dashboard</li>
                <li>Upload your product data (CSV/Excel)</li>
                <li>Select products you want to export</li>
                <li>Click "Add to Workspace" to bring them here</li>
            </ol>
            <div style="margin-top: 2rem;">
                <a href="index.html" class="btn">Go to Dashboard</a>
                <a href="upload.html" class="btn secondary" style="margin-left: 1rem;">Upload Products</a>
            </div>
        </div>

        <div class="products-list" id="products-list" style="display: none;">
            <table class="products-table">
                <thead>
                    <tr>
                        <th>Images</th>
                        <th>Product Title</th>
                        <th>Description</th>
                        <th>Price</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody id="products-tbody">
                </tbody>
            </table>
        </div>
    </main>

    <!-- Toast Container -->
    <div id="toast-container"></div>

    <script src="assets/js/app.js"></script>
    <script src="assets/js/workspace.js"></script>
</body>
</html>
